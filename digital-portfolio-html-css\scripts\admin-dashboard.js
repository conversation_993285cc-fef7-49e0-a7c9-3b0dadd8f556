// Admin Dashboard JavaScript
// Handles admin-specific functionality, user management, and system monitoring

class AdminDashboard {
    constructor() {
        this.init();
    }

    init() {
        // Check admin access
        if (!authSystem.requireAuth('admin')) {
            return;
        }

        this.loadDashboardData();
        this.setupEventListeners();
    }

    loadDashboardData() {
        this.updateUserStats();
        this.loadPendingApprovals();
        this.updateSystemMetrics();
    }

    updateUserStats() {
        const users = authSystem.getUsersFromStorage();
        
        const totalUsers = users.length;
        const freelancers = users.filter(u => u.role === 'freelancer');
        const activeFreelancers = freelancers.filter(u => u.status === 'active').length;
        const pendingFreelancers = freelancers.filter(u => u.status === 'pending').length;
        
        // Update stats display
        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('activeFreelancers').textContent = activeFreelancers;
        document.getElementById('pendingFreelancers').textContent = `${pendingFreelancers} pending approval`;
        
        // Mock active projects count
        document.getElementById('activeProjects').textContent = Math.floor(Math.random() * 50) + 20;
    }

    loadPendingApprovals() {
        const users = authSystem.getUsersFromStorage();
        const pendingFreelancers = users.filter(u => u.role === 'freelancer' && u.status === 'pending');
        
        const pendingContainer = document.getElementById('pendingApprovals');
        const pendingCount = document.getElementById('pendingCount');
        
        pendingCount.textContent = pendingFreelancers.length;
        
        if (pendingFreelancers.length === 0) {
            pendingContainer.innerHTML = '<p class="no-data">No pending approvals</p>';
            return;
        }
        
        pendingContainer.innerHTML = pendingFreelancers.map(user => `
            <div class="approval-item">
                <div class="approval-user">
                    <img src="${user.profileData.avatar}" alt="${user.fullName}" class="approval-avatar">
                    <div class="approval-info">
                        <h4>${user.fullName}</h4>
                        <p>${user.email}</p>
                        <span class="approval-date">Applied: ${new Date(user.createdAt).toLocaleDateString()}</span>
                    </div>
                </div>
                <div class="approval-actions">
                    <button class="btn btn-sm btn-primary" onclick="adminDashboard.approveUser('${user.id}')">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="adminDashboard.rejectUser('${user.id}')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                </div>
            </div>
        `).join('');
    }

    async approveUser(userId) {
        const users = authSystem.getUsersFromStorage();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            users[userIndex].status = 'active';
            localStorage.setItem('portfolioPro_users', JSON.stringify(users));
            
            this.showNotification(`User ${users[userIndex].fullName} has been approved!`, 'success');
            this.loadPendingApprovals();
            this.updateUserStats();
        }
    }

    async rejectUser(userId) {
        if (!confirm('Are you sure you want to reject this user? This action cannot be undone.')) {
            return;
        }
        
        const users = authSystem.getUsersFromStorage();
        const filteredUsers = users.filter(u => u.id !== userId);
        localStorage.setItem('portfolioPro_users', JSON.stringify(filteredUsers));
        
        this.showNotification('User has been rejected and removed from the system.', 'info');
        this.loadPendingApprovals();
        this.updateUserStats();
    }

    updateSystemMetrics() {
        // Mock system health data
        const metrics = document.querySelectorAll('.metric-value');
        metrics.forEach(metric => {
            metric.className = 'metric-value online';
        });
    }

    showUserManagement() {
        const modal = document.getElementById('userManagementModal');
        modal.style.display = 'block';
        this.loadUsersList();
    }

    loadUsersList() {
        const users = authSystem.getUsersFromStorage();
        const usersList = document.getElementById('usersList');
        
        if (users.length === 0) {
            usersList.innerHTML = '<p class="no-data">No users found</p>';
            return;
        }
        
        usersList.innerHTML = users.map(user => `
            <div class="user-item">
                <div class="user-info">
                    <img src="${user.profileData.avatar}" alt="${user.fullName}" class="user-avatar">
                    <div class="user-details">
                        <h4>${user.fullName}</h4>
                        <p>${user.email}</p>
                        <div class="user-meta">
                            <span class="user-role ${user.role}">${user.role}</span>
                            <span class="user-status ${user.status}">${user.status}</span>
                            <span class="user-date">Joined: ${new Date(user.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                </div>
                <div class="user-actions">
                    ${user.status === 'active' ? 
                        `<button class="btn btn-sm btn-warning" onclick="adminDashboard.suspendUser('${user.id}')">
                            <i class="fas fa-ban"></i> Suspend
                        </button>` :
                        user.status === 'suspended' ?
                        `<button class="btn btn-sm btn-primary" onclick="adminDashboard.activateUser('${user.id}')">
                            <i class="fas fa-check"></i> Activate
                        </button>` :
                        ''
                    }
                    <button class="btn btn-sm btn-outline" onclick="adminDashboard.viewUserDetails('${user.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                </div>
            </div>
        `).join('');
    }

    suspendUser(userId) {
        if (!confirm('Are you sure you want to suspend this user?')) {
            return;
        }
        
        const users = authSystem.getUsersFromStorage();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            users[userIndex].status = 'suspended';
            localStorage.setItem('portfolioPro_users', JSON.stringify(users));
            
            this.showNotification(`User ${users[userIndex].fullName} has been suspended.`, 'warning');
            this.loadUsersList();
            this.updateUserStats();
        }
    }

    activateUser(userId) {
        const users = authSystem.getUsersFromStorage();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            users[userIndex].status = 'active';
            localStorage.setItem('portfolioPro_users', JSON.stringify(users));
            
            this.showNotification(`User ${users[userIndex].fullName} has been activated.`, 'success');
            this.loadUsersList();
            this.updateUserStats();
        }
    }

    viewUserDetails(userId) {
        const users = authSystem.getUsersFromStorage();
        const user = users.find(u => u.id === userId);
        
        if (user) {
            alert(`User Details:\n\nName: ${user.fullName}\nEmail: ${user.email}\nRole: ${user.role}\nStatus: ${user.status}\nJoined: ${new Date(user.createdAt).toLocaleDateString()}`);
        }
    }

    showReports() {
        alert('Reports functionality coming soon!');
    }

    showDisputes() {
        alert('Dispute resolution functionality coming soon!');
    }

    showSystemSettings() {
        alert('System settings functionality coming soon!');
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.style.display = 'none';
    }

    setupEventListeners() {
        // User management filters
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');
        
        if (roleFilter) {
            roleFilter.addEventListener('change', () => this.filterUsers());
        }
        
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterUsers());
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // User menu dropdown
        const userMenu = document.querySelector('.user-menu');
        if (userMenu) {
            userMenu.addEventListener('click', () => {
                const dropdown = userMenu.querySelector('.dropdown-menu');
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });
        }
    }

    filterUsers() {
        const roleFilter = document.getElementById('roleFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        
        let users = authSystem.getUsersFromStorage();
        
        if (roleFilter) {
            users = users.filter(u => u.role === roleFilter);
        }
        
        if (statusFilter) {
            users = users.filter(u => u.status === statusFilter);
        }
        
        this.displayFilteredUsers(users);
    }

    displayFilteredUsers(users) {
        const usersList = document.getElementById('usersList');
        
        if (users.length === 0) {
            usersList.innerHTML = '<p class="no-data">No users match the selected filters</p>';
            return;
        }
        
        // Use the same display logic as loadUsersList but with filtered users
        usersList.innerHTML = users.map(user => `
            <div class="user-item">
                <div class="user-info">
                    <img src="${user.profileData.avatar}" alt="${user.fullName}" class="user-avatar">
                    <div class="user-details">
                        <h4>${user.fullName}</h4>
                        <p>${user.email}</p>
                        <div class="user-meta">
                            <span class="user-role ${user.role}">${user.role}</span>
                            <span class="user-status ${user.status}">${user.status}</span>
                            <span class="user-date">Joined: ${new Date(user.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                </div>
                <div class="user-actions">
                    ${user.status === 'active' ? 
                        `<button class="btn btn-sm btn-warning" onclick="adminDashboard.suspendUser('${user.id}')">
                            <i class="fas fa-ban"></i> Suspend
                        </button>` :
                        user.status === 'suspended' ?
                        `<button class="btn btn-sm btn-primary" onclick="adminDashboard.activateUser('${user.id}')">
                            <i class="fas fa-check"></i> Activate
                        </button>` :
                        ''
                    }
                    <button class="btn btn-sm btn-outline" onclick="adminDashboard.viewUserDetails('${user.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                </div>
            </div>
        `).join('');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Global functions for HTML onclick handlers
function showUserManagement() {
    adminDashboard.showUserManagement();
}

function showReports() {
    adminDashboard.showReports();
}

function showDisputes() {
    adminDashboard.showDisputes();
}

function showSystemSettings() {
    adminDashboard.showSystemSettings();
}

function closeModal(modalId) {
    adminDashboard.closeModal(modalId);
}

// Initialize admin dashboard
const adminDashboard = new AdminDashboard();
