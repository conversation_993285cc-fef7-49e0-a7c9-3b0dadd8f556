// Client Dashboard JavaScript
// Handles client-specific functionality, project posting, freelancer browsing, and project management

class ClientDashboard {
    constructor() {
        this.projects = [];
        this.proposals = [];
        this.freelancers = [];
        this.filteredFreelancers = [];
        this.currentView = 'grid';
        this.init();
    }

    init() {
        // Check client access
        if (!authSystem.requireAuth('client')) {
            return;
        }

        this.loadUserInfo();
        this.loadDashboardData();
        this.loadFreelancers();
        this.setupEventListeners();
        this.renderFreelancers();
    }

    loadUserInfo() {
        const currentUser = authSystem.getCurrentUser();
        if (currentUser) {
            document.getElementById('clientName').textContent = currentUser.fullName;
            document.getElementById('welcomeName').textContent = currentUser.fullName;
        }
    }

    loadDashboardData() {
        this.loadFreelancers();
    }

    loadProjects() {
        // Load projects from localStorage (simulating database)
        const allProjects = JSON.parse(localStorage.getItem('portfolioPro_projects') || '[]');
        const currentUser = authSystem.getCurrentUser();
        this.projects = allProjects.filter(p => p.clientId === currentUser.id);
        
        this.displayActiveProjects();
    }

    displayActiveProjects() {
        const activeProjects = this.projects.filter(p => p.status === 'active' || p.status === 'in-progress');
        const container = document.getElementById('activeProjectsList');
        
        if (activeProjects.length === 0) {
            container.innerHTML = '<div class="no-data">No active projects. <a href="#" onclick="showPostProjectModal()">Post your first project!</a></div>';
            return;
        }
        
        container.innerHTML = activeProjects.map(project => `
            <div class="project-item">
                <div class="project-info">
                    <h4>${project.title}</h4>
                    <p>${project.description.substring(0, 100)}...</p>
                    <div class="project-meta">
                        <span class="project-status ${project.status}">${project.status}</span>
                        <span class="project-budget">Budget: ${project.budget}</span>
                        <span class="project-proposals">${project.proposalCount || 0} proposals</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="btn btn-sm btn-outline" onclick="clientDashboard.viewProject('${project.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                </div>
            </div>
        `).join('');
    }

    loadProposals() {
        // Load proposals for client's projects
        const allProposals = JSON.parse(localStorage.getItem('portfolioPro_proposals') || '[]');
        const projectIds = this.projects.map(p => p.id);
        this.proposals = allProposals.filter(p => projectIds.includes(p.projectId));
        
        this.displayRecentProposals();
    }

    displayRecentProposals() {
        const recentProposals = this.proposals.slice(0, 5);
        const container = document.getElementById('proposalsList');
        const countElement = document.getElementById('proposalCount');
        
        countElement.textContent = this.proposals.length;
        
        if (recentProposals.length === 0) {
            container.innerHTML = '<div class="no-data">No proposals yet</div>';
            return;
        }
        
        container.innerHTML = recentProposals.map(proposal => `
            <div class="proposal-item">
                <div class="proposal-info">
                    <h4>${proposal.freelancerName}</h4>
                    <p>Project: ${proposal.projectTitle}</p>
                    <div class="proposal-meta">
                        <span class="proposal-amount">$${proposal.amount}</span>
                        <span class="proposal-duration">${proposal.duration}</span>
                        <span class="proposal-date">${new Date(proposal.createdAt).toLocaleDateString()}</span>
                    </div>
                </div>
                <div class="proposal-actions">
                    <button class="btn btn-sm btn-primary" onclick="clientDashboard.acceptProposal('${proposal.id}')">
                        Accept
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="clientDashboard.viewProposal('${proposal.id}')">
                        View
                    </button>
                </div>
            </div>
        `).join('');
    }

    loadRecommendedFreelancers() {
        // Get active freelancers from users
        const users = authSystem.getUsersFromStorage();
        const freelancers = users.filter(u => u.role === 'freelancer' && u.status === 'active');
        
        const container = document.getElementById('recommendedFreelancers');
        
        if (freelancers.length === 0) {
            container.innerHTML = '<div class="no-data">No freelancers available</div>';
            return;
        }
        
        // Show top 3 freelancers
        const topFreelancers = freelancers.slice(0, 3);
        
        container.innerHTML = topFreelancers.map(freelancer => `
            <div class="freelancer-card-small">
                <img src="${freelancer.profileData.avatar}" alt="${freelancer.fullName}" class="freelancer-avatar-small">
                <div class="freelancer-info-small">
                    <h4>${freelancer.fullName}</h4>
                    <p>Full Stack Developer</p>
                    <div class="freelancer-rating-small">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span>4.9</span>
                    </div>
                </div>
                <button class="btn btn-sm btn-primary" onclick="clientDashboard.contactFreelancer('${freelancer.id}')">
                    Contact
                </button>
            </div>
        `).join('');
    }

    updateStats() {
        const activeProjects = this.projects.filter(p => p.status === 'active' || p.status === 'in-progress').length;
        const completedProjects = this.projects.filter(p => p.status === 'completed').length;
        const totalSpent = this.projects.reduce((sum, p) => sum + (p.paidAmount || 0), 0);
        
        document.getElementById('activeProjects').textContent = activeProjects;
        document.getElementById('completedProjects').textContent = completedProjects;
        document.getElementById('totalSpent').textContent = `$${totalSpent.toLocaleString()}`;
        document.getElementById('hiredFreelancers').textContent = this.projects.filter(p => p.freelancerId).length;
    }

    showPostProjectModal() {
        const modal = document.getElementById('postProjectModal');
        modal.style.display = 'block';
    }

    async postProject(formData) {
        const currentUser = authSystem.getCurrentUser();
        const project = {
            id: 'project_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            clientId: currentUser.id,
            clientName: currentUser.fullName,
            title: formData.get('title'),
            description: formData.get('description'),
            budget: formData.get('budget'),
            duration: formData.get('duration'),
            skills: formData.get('skills').split(',').map(s => s.trim()).filter(s => s),
            category: formData.get('category'),
            status: 'active',
            proposalCount: 0,
            createdAt: new Date().toISOString()
        };
        
        // Save to localStorage (simulating database)
        const allProjects = JSON.parse(localStorage.getItem('portfolioPro_projects') || '[]');
        allProjects.push(project);
        localStorage.setItem('portfolioPro_projects', JSON.stringify(allProjects));
        
        this.projects.push(project);
        this.displayActiveProjects();
        this.updateStats();
        
        this.showNotification('Project posted successfully!', 'success');
        this.closeModal('postProjectModal');
    }

    showBrowseFreelancers() {
        const modal = document.getElementById('browseFreelancersModal');
        modal.style.display = 'block';
        this.loadFreelancersList();
    }

    loadFreelancersList() {
        const users = authSystem.getUsersFromStorage();
        const freelancers = users.filter(u => u.role === 'freelancer' && u.status === 'active');
        
        const container = document.getElementById('freelancersList');
        
        if (freelancers.length === 0) {
            container.innerHTML = '<div class="no-data">No freelancers available</div>';
            return;
        }
        
        container.innerHTML = freelancers.map(freelancer => `
            <div class="freelancer-card">
                <div class="freelancer-avatar">
                    <img src="${freelancer.profileData.avatar}" alt="${freelancer.fullName}">
                </div>
                <div class="freelancer-info">
                    <h3>${freelancer.fullName}</h3>
                    <p class="freelancer-title">Full Stack Developer</p>
                    <div class="freelancer-rating">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span>4.9 (25 reviews)</span>
                    </div>
                    <div class="freelancer-skills">
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Node.js</span>
                        <span class="skill-tag">MongoDB</span>
                    </div>
                    <div class="freelancer-rate">
                        <span>$50/hour</span>
                    </div>
                </div>
                <div class="freelancer-actions">
                    <button class="btn btn-primary" onclick="clientDashboard.contactFreelancer('${freelancer.id}')">
                        Contact
                    </button>
                    <button class="btn btn-outline" onclick="clientDashboard.viewFreelancerProfile('${freelancer.id}')">
                        View Profile
                    </button>
                </div>
            </div>
        `).join('');
    }

    viewProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (project) {
            alert(`Project Details:\n\nTitle: ${project.title}\nDescription: ${project.description}\nBudget: ${project.budget}\nStatus: ${project.status}\nProposals: ${project.proposalCount || 0}`);
        }
    }

    acceptProposal(proposalId) {
        if (!confirm('Are you sure you want to accept this proposal?')) {
            return;
        }
        
        // Update proposal status
        const allProposals = JSON.parse(localStorage.getItem('portfolioPro_proposals') || '[]');
        const proposalIndex = allProposals.findIndex(p => p.id === proposalId);
        
        if (proposalIndex !== -1) {
            allProposals[proposalIndex].status = 'accepted';
            localStorage.setItem('portfolioPro_proposals', JSON.stringify(allProposals));
            
            this.showNotification('Proposal accepted! The freelancer has been notified.', 'success');
            this.loadProposals();
        }
    }

    viewProposal(proposalId) {
        const proposal = this.proposals.find(p => p.id === proposalId);
        if (proposal) {
            alert(`Proposal Details:\n\nFreelancer: ${proposal.freelancerName}\nAmount: $${proposal.amount}\nDuration: ${proposal.duration}\nMessage: ${proposal.message}`);
        }
    }

    contactFreelancer(freelancerId) {
        alert('Messaging functionality coming soon!');
    }

    viewFreelancerProfile(freelancerId) {
        alert('Freelancer profile view coming soon!');
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.style.display = 'none';
    }

    setupEventListeners() {
        // Setup freelancer search functionality
        this.setupFreelancerSearch();

        // Post project form
        const postProjectForm = document.getElementById('postProjectForm');
        if (postProjectForm) {
            postProjectForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(postProjectForm);
                
                const submitBtn = postProjectForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Posting...';
                submitBtn.disabled = true;
                
                try {
                    await this.postProject(formData);
                    postProjectForm.reset();
                } catch (error) {
                    this.showNotification('Failed to post project. Please try again.', 'error');
                } finally {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            });
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // User menu dropdown
        const userMenu = document.querySelector('.user-menu');
        if (userMenu) {
            userMenu.addEventListener('click', () => {
                const dropdown = userMenu.querySelector('.dropdown-menu');
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Global functions for HTML onclick handlers
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}

function closeModal(modalId) {
    clientDashboard.closeModal(modalId);
}

// Freelancer search and browsing functionality
ClientDashboard.prototype.loadFreelancers = function() {
    this.freelancers = [
        {
            id: 1,
            name: 'Eleni Birhan',
            title: 'Full Stack Web Developer',
            skills: ['web-development', 'ui-ux-design'],
            experience: 'senior',
            availability: 'available',
            rating: 4.9,
            reviews: 127,
            hourlyRate: 45,
            location: 'Addis Ababa, Ethiopia',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
            description: 'Experienced full-stack developer specializing in React, Node.js, and modern web technologies.',
            portfolio: ['E-commerce Platform', 'Healthcare Management System', 'Real Estate Portal'],
            completedProjects: 89,
            responseTime: '2 hours'
        },
        {
            id: 2,
            name: 'Hawinet Mekonen',
            title: 'UI/UX Designer & Frontend Developer',
            skills: ['ui-ux-design', 'graphic-design'],
            experience: 'mid',
            availability: 'available',
            rating: 4.8,
            reviews: 94,
            hourlyRate: 35,
            location: 'Addis Ababa, Ethiopia',
            avatar: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
            description: 'Creative UI/UX designer with expertise in user-centered design and modern frontend frameworks.',
            portfolio: ['Mobile Banking App', 'Travel Booking Platform', 'Educational Dashboard'],
            completedProjects: 67,
            responseTime: '1 hour'
        },
        {
            id: 3,
            name: 'Mahilet Ashenafi',
            title: 'Digital Marketing Specialist',
            skills: ['digital-marketing', 'content-writing'],
            experience: 'mid',
            availability: 'busy',
            rating: 4.7,
            reviews: 156,
            hourlyRate: 30,
            location: 'Addis Ababa, Ethiopia',
            avatar: 'https://images.unsplash.com/photo-*************-53994a69daeb?w=150&h=150&fit=crop&crop=face',
            description: 'Results-driven digital marketer with proven track record in SEO, social media, and content strategy.',
            portfolio: ['Brand Campaign Strategy', 'Social Media Growth', 'Content Marketing Plan'],
            completedProjects: 134,
            responseTime: '4 hours'
        },
        {
            id: 4,
            name: 'Miskir Tamire',
            title: 'Mobile App Developer',
            skills: ['mobile-development', 'ui-ux-design'],
            experience: 'senior',
            availability: 'available',
            rating: 4.9,
            reviews: 203,
            hourlyRate: 50,
            location: 'Addis Ababa, Ethiopia',
            avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
            description: 'Expert mobile developer specializing in React Native and Flutter for cross-platform solutions.',
            portfolio: ['Food Delivery App', 'Fitness Tracking App', 'Financial Management App'],
            completedProjects: 112,
            responseTime: '1 hour'
        }
    ];
    this.filteredFreelancers = [...this.freelancers];
};

ClientDashboard.prototype.setupFreelancerSearch = function() {
    // Search input
    const searchInput = document.getElementById('freelancerSearch');
    if (searchInput) {
        searchInput.addEventListener('input', () => this.handleSearch());
    }

    // Filter selects
    const filters = ['skillFilter', 'experienceFilter', 'availabilityFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', () => this.handleFilter());
        }
    });

    // Clear filters button
    const clearBtn = document.getElementById('clearFilters');
    if (clearBtn) {
        clearBtn.addEventListener('click', () => this.clearFilters());
    }

    // View toggle buttons
    const viewBtns = document.querySelectorAll('.view-btn');
    viewBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const view = e.currentTarget.dataset.view;
            this.switchView(view);
        });
    });
};

ClientDashboard.prototype.handleSearch = function() {
    const searchTerm = document.getElementById('freelancerSearch').value.toLowerCase();
    this.applyFilters(searchTerm);
};

ClientDashboard.prototype.handleFilter = function() {
    const searchTerm = document.getElementById('freelancerSearch').value.toLowerCase();
    this.applyFilters(searchTerm);
};

ClientDashboard.prototype.applyFilters = function(searchTerm = '') {
    const skillFilter = document.getElementById('skillFilter')?.value || '';
    const experienceFilter = document.getElementById('experienceFilter')?.value || '';
    const availabilityFilter = document.getElementById('availabilityFilter')?.value || '';

    this.filteredFreelancers = this.freelancers.filter(freelancer => {
        // Search term filter
        const matchesSearch = !searchTerm ||
            freelancer.name.toLowerCase().includes(searchTerm) ||
            freelancer.title.toLowerCase().includes(searchTerm) ||
            freelancer.description.toLowerCase().includes(searchTerm) ||
            freelancer.skills.some(skill => skill.toLowerCase().includes(searchTerm));

        // Skill filter
        const matchesSkill = !skillFilter || freelancer.skills.includes(skillFilter);

        // Experience filter
        const matchesExperience = !experienceFilter || freelancer.experience === experienceFilter;

        // Availability filter
        const matchesAvailability = !availabilityFilter || freelancer.availability === availabilityFilter;

        return matchesSearch && matchesSkill && matchesExperience && matchesAvailability;
    });

    this.renderFreelancers();
};

ClientDashboard.prototype.clearFilters = function() {
    const searchInput = document.getElementById('freelancerSearch');
    const skillFilter = document.getElementById('skillFilter');
    const experienceFilter = document.getElementById('experienceFilter');
    const availabilityFilter = document.getElementById('availabilityFilter');

    if (searchInput) searchInput.value = '';
    if (skillFilter) skillFilter.value = '';
    if (experienceFilter) experienceFilter.value = '';
    if (availabilityFilter) availabilityFilter.value = '';

    this.filteredFreelancers = [...this.freelancers];
    this.renderFreelancers();
};

ClientDashboard.prototype.switchView = function(view) {
    this.currentView = view;

    // Update active button
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    const activeBtn = document.querySelector(`[data-view="${view}"]`);
    if (activeBtn) activeBtn.classList.add('active');

    // Update grid class
    const grid = document.getElementById('freelancersGrid');
    if (grid) {
        grid.className = view === 'grid' ? 'freelancers-grid' : 'freelancers-list';
    }

    this.renderFreelancers();
};

ClientDashboard.prototype.renderFreelancers = function() {
    const container = document.getElementById('freelancersGrid');
    const noResults = document.getElementById('noResults');

    if (!container) return;

    if (this.filteredFreelancers.length === 0) {
        container.innerHTML = '';
        if (noResults) noResults.style.display = 'block';
        return;
    }

    if (noResults) noResults.style.display = 'none';

    const freelancersHTML = this.filteredFreelancers.map(freelancer => {
        return this.currentView === 'grid'
            ? this.createFreelancerCard(freelancer)
            : this.createFreelancerListItem(freelancer);
    }).join('');

    container.innerHTML = freelancersHTML;
};

ClientDashboard.prototype.createFreelancerCard = function(freelancer) {
    const availabilityClass = freelancer.availability === 'available' ? 'available' : 'busy';
    const availabilityText = freelancer.availability === 'available' ? 'Available Now' : 'Busy';

    return `
        <div class="freelancer-card" data-id="${freelancer.id}">
            <div class="freelancer-header">
                <img src="${freelancer.avatar}" alt="${freelancer.name}" class="freelancer-avatar">
                <div class="availability-badge ${availabilityClass}">
                    <i class="fas fa-circle"></i>
                    ${availabilityText}
                </div>
            </div>

            <div class="freelancer-info">
                <h3 class="freelancer-name">${freelancer.name}</h3>
                <p class="freelancer-title">${freelancer.title}</p>
                <p class="freelancer-location">
                    <i class="fas fa-map-marker-alt"></i>
                    ${freelancer.location}
                </p>
            </div>

            <div class="freelancer-stats">
                <div class="stat">
                    <i class="fas fa-star"></i>
                    <span>${freelancer.rating}</span>
                    <small>(${freelancer.reviews} reviews)</small>
                </div>
                <div class="stat">
                    <i class="fas fa-clock"></i>
                    <span>Response: ${freelancer.responseTime}</span>
                </div>
            </div>

            <div class="freelancer-skills">
                ${freelancer.skills.slice(0, 3).map(skill =>
                    `<span class="skill-tag">${this.formatSkill(skill)}</span>`
                ).join('')}
            </div>

            <div class="freelancer-description">
                <p>${freelancer.description}</p>
            </div>

            <div class="freelancer-footer">
                <div class="hourly-rate">
                    <strong>$${freelancer.hourlyRate}/hr</strong>
                </div>
                <div class="freelancer-actions">
                    <button class="btn btn-outline btn-sm" onclick="viewFreelancerProfile(${freelancer.id})">
                        <i class="fas fa-eye"></i>
                        View Profile
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="contactFreelancer(${freelancer.id})">
                        <i class="fas fa-envelope"></i>
                        Contact
                    </button>
                </div>
            </div>
        </div>
    `;
};

ClientDashboard.prototype.createFreelancerListItem = function(freelancer) {
    const availabilityClass = freelancer.availability === 'available' ? 'available' : 'busy';
    const availabilityText = freelancer.availability === 'available' ? 'Available Now' : 'Busy';

    return `
        <div class="freelancer-list-item" data-id="${freelancer.id}">
            <div class="freelancer-list-avatar">
                <img src="${freelancer.avatar}" alt="${freelancer.name}">
                <div class="availability-badge ${availabilityClass}">
                    <i class="fas fa-circle"></i>
                </div>
            </div>

            <div class="freelancer-list-info">
                <div class="freelancer-list-header">
                    <h3>${freelancer.name}</h3>
                    <div class="freelancer-list-rating">
                        <i class="fas fa-star"></i>
                        <span>${freelancer.rating}</span>
                        <small>(${freelancer.reviews})</small>
                    </div>
                </div>

                <p class="freelancer-list-title">${freelancer.title}</p>
                <p class="freelancer-list-description">${freelancer.description}</p>

                <div class="freelancer-list-meta">
                    <span class="location">
                        <i class="fas fa-map-marker-alt"></i>
                        ${freelancer.location}
                    </span>
                    <span class="response-time">
                        <i class="fas fa-clock"></i>
                        Response: ${freelancer.responseTime}
                    </span>
                    <span class="projects">
                        <i class="fas fa-check-circle"></i>
                        ${freelancer.completedProjects} projects completed
                    </span>
                </div>

                <div class="freelancer-list-skills">
                    ${freelancer.skills.map(skill =>
                        `<span class="skill-tag">${this.formatSkill(skill)}</span>`
                    ).join('')}
                </div>
            </div>

            <div class="freelancer-list-actions">
                <div class="hourly-rate">
                    <strong>$${freelancer.hourlyRate}/hr</strong>
                </div>
                <div class="availability-status ${availabilityClass}">
                    ${availabilityText}
                </div>
                <div class="action-buttons">
                    <button class="btn btn-outline btn-sm" onclick="viewFreelancerProfile(${freelancer.id})">
                        <i class="fas fa-eye"></i>
                        View Profile
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="contactFreelancer(${freelancer.id})">
                        <i class="fas fa-envelope"></i>
                        Contact
                    </button>
                </div>
            </div>
        </div>
    `;
};

ClientDashboard.prototype.formatSkill = function(skill) {
    return skill.split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
};

ClientDashboard.prototype.getFreelancerById = function(id) {
    return this.freelancers.find(f => f.id === parseInt(id));
};

// Global functions for button actions
function viewFreelancerProfile(freelancerId) {
    const freelancer = clientDashboard.getFreelancerById(freelancerId);
    if (freelancer) {
        showFreelancerProfileModal(freelancer);
    }
}

function contactFreelancer(freelancerId) {
    const freelancer = clientDashboard.getFreelancerById(freelancerId);
    if (freelancer) {
        showContactFreelancerModal(freelancer);
    }
}

// Modal functions
function showFreelancerProfileModal(freelancer) {
    // Create modal HTML
    const modalHTML = `
        <div id="freelancerProfileModal" class="modal" style="display: block;">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user"></i> ${freelancer.name}</h2>
                    <span class="close" onclick="closeModal('freelancerProfileModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="freelancer-profile">
                        <div class="profile-header">
                            <img src="${freelancer.avatar}" alt="${freelancer.name}" class="profile-avatar">
                            <div class="profile-info">
                                <h3>${freelancer.name}</h3>
                                <p class="profile-title">${freelancer.title}</p>
                                <p class="profile-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    ${freelancer.location}
                                </p>
                                <div class="profile-stats">
                                    <div class="stat">
                                        <i class="fas fa-star"></i>
                                        <span>${freelancer.rating}</span>
                                        <small>(${freelancer.reviews} reviews)</small>
                                    </div>
                                    <div class="stat">
                                        <i class="fas fa-check-circle"></i>
                                        <span>${freelancer.completedProjects} projects completed</span>
                                    </div>
                                    <div class="stat">
                                        <i class="fas fa-clock"></i>
                                        <span>Response time: ${freelancer.responseTime}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="profile-actions">
                                <div class="hourly-rate-large">
                                    <strong>$${freelancer.hourlyRate}/hr</strong>
                                </div>
                                <div class="availability-badge ${freelancer.availability}">
                                    <i class="fas fa-circle"></i>
                                    ${freelancer.availability === 'available' ? 'Available Now' : 'Busy'}
                                </div>
                            </div>
                        </div>

                        <div class="profile-content">
                            <div class="profile-section">
                                <h4><i class="fas fa-info-circle"></i> About</h4>
                                <p>${freelancer.description}</p>
                            </div>

                            <div class="profile-section">
                                <h4><i class="fas fa-cogs"></i> Skills</h4>
                                <div class="skills-list">
                                    ${freelancer.skills.map(skill =>
                                        `<span class="skill-tag">${clientDashboard.formatSkill(skill)}</span>`
                                    ).join('')}
                                </div>
                            </div>

                            <div class="profile-section">
                                <h4><i class="fas fa-briefcase"></i> Recent Projects</h4>
                                <div class="portfolio-list">
                                    ${freelancer.portfolio.map(project =>
                                        `<div class="portfolio-item">
                                            <i class="fas fa-project-diagram"></i>
                                            <span>${project}</span>
                                        </div>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('freelancerProfileModal')">
                        <i class="fas fa-times"></i>
                        Close
                    </button>
                    <button class="btn btn-primary" onclick="contactFreelancer(${freelancer.id}); closeModal('freelancerProfileModal');">
                        <i class="fas fa-envelope"></i>
                        Contact ${freelancer.name}
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('freelancerProfileModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function showContactFreelancerModal(freelancer) {
    const modalHTML = `
        <div id="contactFreelancerModal" class="modal" style="display: block;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-envelope"></i> Contact ${freelancer.name}</h2>
                    <span class="close" onclick="closeModal('contactFreelancerModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="contact-freelancer-info">
                        <div class="freelancer-summary">
                            <img src="${freelancer.avatar}" alt="${freelancer.name}" class="contact-avatar">
                            <div class="contact-info">
                                <h4>${freelancer.name}</h4>
                                <p>${freelancer.title}</p>
                                <div class="contact-rate">$${freelancer.hourlyRate}/hr</div>
                            </div>
                        </div>
                    </div>

                    <form id="contactFreelancerForm" class="contact-form">
                        <div class="form-group">
                            <label for="projectTitle">Project Title</label>
                            <input type="text" id="projectTitle" name="projectTitle" required
                                   placeholder="Enter your project title">
                        </div>

                        <div class="form-group">
                            <label for="projectBudget">Budget Range</label>
                            <select id="projectBudget" name="projectBudget" required>
                                <option value="">Select budget range</option>
                                <option value="under-500">Under $500</option>
                                <option value="500-1000">$500 - $1,000</option>
                                <option value="1000-2500">$1,000 - $2,500</option>
                                <option value="2500-5000">$2,500 - $5,000</option>
                                <option value="5000-10000">$5,000 - $10,000</option>
                                <option value="over-10000">Over $10,000</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="projectTimeline">Timeline</label>
                            <select id="projectTimeline" name="projectTimeline" required>
                                <option value="">Select timeline</option>
                                <option value="asap">ASAP</option>
                                <option value="1-week">Within 1 week</option>
                                <option value="2-weeks">Within 2 weeks</option>
                                <option value="1-month">Within 1 month</option>
                                <option value="2-months">Within 2 months</option>
                                <option value="flexible">Flexible</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="projectDescription">Project Description</label>
                            <textarea id="projectDescription" name="projectDescription" rows="5" required
                                      placeholder="Describe your project requirements, goals, and any specific details..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="contactEmail">Your Email</label>
                            <input type="email" id="contactEmail" name="contactEmail" required
                                   placeholder="<EMAIL>">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('contactFreelancerModal')">
                        <i class="fas fa-times"></i>
                        Cancel
                    </button>
                    <button class="btn btn-primary" onclick="sendContactMessage(${freelancer.id})">
                        <i class="fas fa-paper-plane"></i>
                        Send Message
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('contactFreelancerModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Pre-fill email if user is logged in
    const currentUser = authSystem.getCurrentUser();
    if (currentUser && currentUser.email) {
        document.getElementById('contactEmail').value = currentUser.email;
    }
}

function sendContactMessage(freelancerId) {
    const form = document.getElementById('contactFreelancerForm');
    const formData = new FormData(form);
    const freelancer = clientDashboard.getFreelancerById(freelancerId);

    // Simulate sending message
    const message = {
        id: Date.now(),
        freelancerId: freelancerId,
        freelancerName: freelancer.name,
        clientId: authSystem.getCurrentUser()?.id,
        clientName: authSystem.getCurrentUser()?.fullName,
        projectTitle: formData.get('projectTitle'),
        projectBudget: formData.get('projectBudget'),
        projectTimeline: formData.get('projectTimeline'),
        projectDescription: formData.get('projectDescription'),
        contactEmail: formData.get('contactEmail'),
        timestamp: new Date().toISOString(),
        status: 'sent'
    };

    // Save to localStorage (simulating database)
    const messages = JSON.parse(localStorage.getItem('portfolioPro_messages') || '[]');
    messages.push(message);
    localStorage.setItem('portfolioPro_messages', JSON.stringify(messages));

    // Show success message
    alert(`Message sent to ${freelancer.name}! They will receive your project details and contact you at ${formData.get('contactEmail')}.`);

    // Close modal
    closeModal('contactFreelancerModal');
}

// Initialize client dashboard
const clientDashboard = new ClientDashboard();
