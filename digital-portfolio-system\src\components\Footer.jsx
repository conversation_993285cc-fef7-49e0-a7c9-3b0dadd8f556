import { Github, Twitter, Linkedin, Mail, MapPin, Phone } from 'lucide-react'

const Footer = () => {
  const footerLinks = {
    platform: [
      { name: 'How it Works', href: '#' },
      { name: 'Browse Freelancers', href: '#' },
      { name: 'Post a Project', href: '#' },
      { name: 'Success Stories', href: '#' }
    ],
    freelancers: [
      { name: 'Create Profile', href: '#' },
      { name: 'Find Work', href: '#' },
      { name: 'Portfolio Tips', href: '#' },
      { name: 'Freelancer Resources', href: '#' }
    ],
    clients: [
      { name: 'Hire Freelancers', href: '#' },
      { name: 'Project Management', href: '#' },
      { name: 'Client Resources', href: '#' },
      { name: 'Enterprise Solutions', href: '#' }
    ],
    support: [
      { name: 'Help Center', href: '#' },
      { name: 'Contact Us', href: '#' },
      { name: 'Community Forum', href: '#' },
      { name: 'API Documentation', href: '#' }
    ]
  }

  const socialLinks = [
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'LinkedIn', icon: Linkedin, href: '#' },
    { name: 'GitHub', icon: Github, href: '#' }
  ]

  return (
    <footer className="bg-gray-900 text-white border-t border-gray-700">
      <div className="container py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand and Description */}
          <div className="lg:col-span-2">
            <h3 className="text-2xl font-bold text-white mb-4">
              PortfolioHub
            </h3>
            <p className="text-gray-300 mb-6 max-w-md">
              The next generation platform for freelancers and clients. 
              Build your digital portfolio, showcase your skills, and connect 
              with opportunities that match your expertise.
            </p>
            <div className="space-y-3 text-gray-300">
              <div className="flex items-center gap-3 group">
                <div className="w-8 h-8 bg-blue-600/20 rounded-lg flex items-center justify-center group-hover:bg-blue-600/30 transition-colors">
                  <Mail className="w-4 h-4 text-blue-400" />
                </div>
                <span className="group-hover:text-white transition-colors">portfoliohub.com</span>
              </div>
              <div className="flex items-center gap-3 group">
                <div className="w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center group-hover:bg-green-600/30 transition-colors">
                  <Phone className="w-4 h-4 text-green-400" />
                </div>
                <span className="group-hover:text-white transition-colors">+251 900000000</span>
              </div>
              <div className="flex items-center gap-3 group">
                <div className="w-8 h-8 bg-purple-600/20 rounded-lg flex items-center justify-center group-hover:bg-purple-600/30 transition-colors">
                  <MapPin className="w-4 h-4 text-purple-400" />
                </div>
                <span className="group-hover:text-white transition-colors">Addis Ababa, ET</span>
              </div>
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">Platform</h4>
            <ul className="space-y-2">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Freelancers Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">For Freelancers</h4>
            <ul className="space-y-2">
              {footerLinks.freelancers.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Clients Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">For Clients</h4>
            <ul className="space-y-2">
              {footerLinks.clients.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">Support</h4>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-300 text-sm">
              © 2025 PortfolioHub. All rights reserved.
            </div>
            
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-4">
                <a href="#" className="text-gray-300 hover:text-white text-sm transition-colors">
                  Privacy Policy
                </a>
                <a href="#" className="text-gray-300 hover:text-white text-sm transition-colors">
                  Terms of Service
                </a>
                <a href="#" className="text-gray-300 hover:text-white text-sm transition-colors">
                  Cookie Policy
                </a>
              </div>
              
              <div className="flex items-center gap-4">
                {socialLinks.map((social) => {
                  const Icon = social.icon
                  const getIconColors = (name) => {
                    switch(name) {
                      case 'Twitter':
                        return 'bg-blue-600/20 hover:bg-blue-600 text-blue-400 hover:text-white'
                      case 'LinkedIn':
                        return 'bg-blue-700/20 hover:bg-blue-700 text-blue-300 hover:text-white'
                      case 'GitHub':
                        return 'bg-gray-700/20 hover:bg-gray-600 text-gray-300 hover:text-white'
                      default:
                        return 'bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white'
                    }
                  }

                  return (
                    <a
                      key={social.name}
                      href={social.href}
                      className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg ${getIconColors(social.name)}`}
                      aria-label={social.name}
                    >
                      <Icon className="w-5 h-5" />
                    </a>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
