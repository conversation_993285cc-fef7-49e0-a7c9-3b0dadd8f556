import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin, Globe, Heart } from 'lucide-react'

const FiverrFooter = () => {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    'Categories': [
      'Graphics & Design',
      'Digital Marketing', 
      'Writing & Translation',
      'Video & Animation',
      'Music & Audio',
      'Programming & Tech',
      'Business',
      'Lifestyle'
    ],
    'About': [
      'Careers',
      'Press & News',
      'Partnerships',
      'Privacy Policy',
      'Terms of Service',
      'Intellectual Property Claims',
      'Investor Relations'
    ],
    'Support': [
      'Help & Support',
      'Trust & Safety',
      'Selling on Fiverr',
      'Buying on Fiverr',
      'Fiverr Guides',
      'Learn',
      'Online Courses'
    ],
    'Community': [
      'Customer Success Stories',
      'Community Hub',
      'Forum',
      'Events',
      'Blog',
      'Influencers',
      'Affiliates',
      'Podcast'
    ]
  }

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook', color: 'hover:bg-blue-600' },
    { icon: Twitter, href: '#', label: 'Twitter', color: 'hover:bg-sky-500' },
    { icon: Instagram, href: '#', label: 'Instagram', color: 'hover:bg-pink-600' },
    { icon: Linkedin, href: '#', label: 'LinkedIn', color: 'hover:bg-blue-700' }
  ]

  return (
    <footer className="bg-white border-t border-gray-200">
      {/* Newsletter Section */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600">
        <div className="container py-12">
          <div className="text-center text-white">
            <h3 className="text-3xl font-bold mb-4">Subscribe to our newsletter</h3>
            <p className="text-xl mb-8 opacity-90">Get the latest updates on new features and special offers</p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-6 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white"
              />
              <button className="px-8 py-3 bg-white text-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-200">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">f</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900">fiverr<span className="text-green-500">.</span></h3>
              </div>
              <p className="text-gray-600 text-sm leading-relaxed mb-6">
                The world's largest marketplace for creative and professional services.
              </p>
            </div>
            
            {/* Contact Info */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-3 text-gray-600">
                <Mail className="w-4 h-4 text-green-500" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-gray-600">
                <Phone className="w-4 h-4 text-green-500" />
                <span className="text-sm">+****************</span>
              </div>
              <div className="flex items-center gap-3 text-gray-600">
                <Globe className="w-4 h-4 text-green-500" />
                <span className="text-sm">Available Worldwide</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex gap-3">
              {socialLinks.map((social, index) => {
                const Icon = social.icon
                return (
                  <a
                    key={index}
                    href={social.href}
                    aria-label={social.label}
                    className={`w-10 h-10 bg-gray-100 ${social.color} rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110`}
                  >
                    <Icon className="w-5 h-5 text-gray-600 hover:text-white transition-colors" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h4 className="font-bold text-gray-900 mb-6">{category}</h4>
              <ul className="space-y-3">
                {links.map((link, index) => (
                  <li key={index}>
                    <a
                      href="#"
                      className="text-gray-600 hover:text-green-600 text-sm transition-colors duration-200 hover:underline"
                    >
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-200 bg-gray-50">
        <div className="container py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              © {currentYear} Fiverr International Ltd. Made with <Heart className="w-4 h-4 text-red-500 fill-current" /> in Tel Aviv
            </div>
            <div className="flex gap-6 text-sm">
              <a href="#" className="text-gray-600 hover:text-green-600 transition-colors hover:underline">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-600 hover:text-green-600 transition-colors hover:underline">
                Terms of Service
              </a>
              <a href="#" className="text-gray-600 hover:text-green-600 transition-colors hover:underline">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default FiverrFooter
