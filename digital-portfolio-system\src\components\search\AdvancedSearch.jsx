import { useState } from 'react'
import { 
  Search, 
  Filter, 
  MapPin, 
  DollarSign, 
  Clock, 
  Star, 
  Briefcase,
  GraduationCap,
  X,
  ChevronDown
} from 'lucide-react'

const AdvancedSearch = ({ onSearch, onFilterChange, filters = {} }) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchQuery, setSearchQuery] = useState(filters.query || '')
  const [localFilters, setLocalFilters] = useState({
    skills: filters.skills || [],
    industry: filters.industry || '',
    experience: filters.experience || '',
    location: filters.location || '',
    priceRange: filters.priceRange || '',
    availability: filters.availability || '',
    rating: filters.rating || '',
    projectType: filters.projectType || '',
    ...filters
  })

  // Mock data for filter options
  const filterOptions = {
    skills: [
      'React', 'Vue.js', 'Angular', 'Node.js', 'Python', 'Java', 'PHP', 'Ruby',
      'JavaScript', 'TypeScript', 'HTML/CSS', 'UI/UX Design', 'Graphic Design',
      'Digital Marketing', 'SEO', 'Content Writing', 'Data Analysis', 'Machine Learning',
      'Mobile Development', 'WordPress', 'Shopify', 'Photography', 'Video Editing'
    ],
    industries: [
      'Technology', 'Healthcare', 'Finance', 'Education', 'E-commerce', 'Marketing',
      'Real Estate', 'Entertainment', 'Non-profit', 'Government', 'Consulting',
      'Manufacturing', 'Retail', 'Travel', 'Food & Beverage'
    ],
    experienceLevels: [
      { value: 'entry', label: 'Entry Level (0-2 years)' },
      { value: 'mid', label: 'Mid Level (2-5 years)' },
      { value: 'senior', label: 'Senior Level (5-10 years)' },
      { value: 'expert', label: 'Expert Level (10+ years)' }
    ],
    priceRanges: [
      { value: '0-25', label: '$0 - $25/hr' },
      { value: '25-50', label: '$25 - $50/hr' },
      { value: '50-100', label: '$50 - $100/hr' },
      { value: '100+', label: '$100+/hr' }
    ],
    projectTypes: [
      'Web Development', 'Mobile App', 'Design', 'Marketing', 'Writing',
      'Data Analysis', 'Consulting', 'Photography', 'Video Production'
    ]
  }

  const handleSkillToggle = (skill) => {
    const updatedSkills = localFilters.skills.includes(skill)
      ? localFilters.skills.filter(s => s !== skill)
      : [...localFilters.skills, skill]
    
    const newFilters = { ...localFilters, skills: updatedSkills }
    setLocalFilters(newFilters)
    onFilterChange && onFilterChange(newFilters)
  }

  const handleFilterChange = (key, value) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    onFilterChange && onFilterChange(newFilters)
  }

  const handleSearch = (e) => {
    e.preventDefault()
    onSearch && onSearch(searchQuery, localFilters)
  }

  const clearFilters = () => {
    const clearedFilters = {
      skills: [],
      industry: '',
      experience: '',
      location: '',
      priceRange: '',
      availability: '',
      rating: '',
      projectType: ''
    }
    setLocalFilters(clearedFilters)
    setSearchQuery('')
    onFilterChange && onFilterChange(clearedFilters)
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (localFilters.skills.length > 0) count++
    if (localFilters.industry) count++
    if (localFilters.experience) count++
    if (localFilters.location) count++
    if (localFilters.priceRange) count++
    if (localFilters.availability) count++
    if (localFilters.rating) count++
    if (localFilters.projectType) count++
    return count
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Main Search Bar */}
      <form onSubmit={handleSearch} className="p-6">
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search portfolios by name, skills, or keywords..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg"
            />
          </div>
          <button
            type="button"
            onClick={() => setIsExpanded(!isExpanded)}
            className={`flex items-center px-4 py-3 border rounded-lg transition-colors ${
              isExpanded ? 'bg-primary-50 border-primary-200 text-primary-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Filter className="w-5 h-5 mr-2" />
            Filters
            {getActiveFilterCount() > 0 && (
              <span className="ml-2 bg-primary-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {getActiveFilterCount()}
              </span>
            )}
            <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          </button>
          <button
            type="submit"
            className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
          >
            Search
          </button>
        </div>
      </form>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Skills */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Skills
              </label>
              <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3 bg-white">
                <div className="space-y-2">
                  {filterOptions.skills.map((skill) => (
                    <label key={skill} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={localFilters.skills.includes(skill)}
                        onChange={() => handleSkillToggle(skill)}
                        className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{skill}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Industry */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Industry
              </label>
              <select
                value={localFilters.industry}
                onChange={(e) => handleFilterChange('industry', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">All Industries</option>
                {filterOptions.industries.map((industry) => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
            </div>

            {/* Experience Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Experience Level
              </label>
              <select
                value={localFilters.experience}
                onChange={(e) => handleFilterChange('experience', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">All Levels</option>
                {filterOptions.experienceLevels.map((level) => (
                  <option key={level.value} value={level.value}>{level.label}</option>
                ))}
              </select>
            </div>

            {/* Location */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <MapPin className="w-4 h-4 inline mr-1" />
                Location
              </label>
              <input
                type="text"
                value={localFilters.location}
                onChange={(e) => handleFilterChange('location', e.target.value)}
                placeholder="City, State, or Country"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <DollarSign className="w-4 h-4 inline mr-1" />
                Hourly Rate
              </label>
              <select
                value={localFilters.priceRange}
                onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Any Rate</option>
                {filterOptions.priceRanges.map((range) => (
                  <option key={range.value} value={range.value}>{range.label}</option>
                ))}
              </select>
            </div>

            {/* Project Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <Briefcase className="w-4 h-4 inline mr-1" />
                Project Type
              </label>
              <select
                value={localFilters.projectType}
                onChange={(e) => handleFilterChange('projectType', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">All Types</option>
                {filterOptions.projectTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Selected Skills Display */}
          {localFilters.skills.length > 0 && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Skills:
              </label>
              <div className="flex flex-wrap gap-2">
                {localFilters.skills.map((skill) => (
                  <span
                    key={skill}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                  >
                    {skill}
                    <button
                      onClick={() => handleSkillToggle(skill)}
                      className="ml-2 text-primary-600 hover:text-primary-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Filter Actions */}
          <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={clearFilters}
              className="text-gray-600 hover:text-gray-800 text-sm font-medium"
            >
              Clear All Filters
            </button>
            <div className="text-sm text-gray-600">
              {getActiveFilterCount()} filter{getActiveFilterCount() !== 1 ? 's' : ''} applied
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdvancedSearch
