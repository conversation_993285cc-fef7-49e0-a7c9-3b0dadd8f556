// Portfolio Manager - Handles project uploads, management, and showcase
class PortfolioManager {
    constructor() {
        this.projects = [];
        this.categories = [
            'Web Development',
            'Mobile Development',
            'UI/UX Design',
            'Graphic Design',
            'Digital Marketing',
            'Content Writing',
            'Video Production',
            'Photography',
            'Data Analysis',
            'Other'
        ];
        this.skills = [
            'JavaScript', 'Python', 'React', 'Node.js', 'HTML/CSS',
            'Photoshop', 'Illustrator', 'Figma', 'WordPress', 'SEO',
            'Social Media', 'Content Strategy', 'Video Editing', 'Photography'
        ];
        this.init();
    }

    init() {
        this.loadProjects();
        this.setupEventListeners();
    }

    // Load projects from localStorage
    loadProjects() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return;

        const userProjects = localStorage.getItem(`portfolio_projects_${currentUser.id}`);
        this.projects = userProjects ? JSON.parse(userProjects) : [];
    }

    // Save projects to localStorage
    saveProjects() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return;

        localStorage.setItem(`portfolio_projects_${currentUser.id}`, JSON.stringify(this.projects));
    }

    // Setup event listeners
    setupEventListeners() {
        // Add project button
        const addProjectBtn = document.getElementById('addProjectBtn');
        if (addProjectBtn) {
            addProjectBtn.addEventListener('click', () => this.showAddProjectModal());
        }

        // File upload handling
        const fileInput = document.getElementById('projectFiles');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }

        // Form submission
        const projectForm = document.getElementById('projectForm');
        if (projectForm) {
            projectForm.addEventListener('submit', (e) => this.handleProjectSubmit(e));
        }
    }

    // Show add project modal
    showAddProjectModal() {
        const modal = document.getElementById('addProjectModal');
        if (modal) {
            modal.style.display = 'flex';
            this.populateFormOptions();
        }
    }

    // Hide add project modal
    hideAddProjectModal() {
        const modal = document.getElementById('addProjectModal');
        if (modal) {
            modal.style.display = 'none';
            this.resetForm();
        }
    }

    // Populate form options
    populateFormOptions() {
        // Populate categories
        const categorySelect = document.getElementById('projectCategory');
        if (categorySelect) {
            categorySelect.innerHTML = '<option value="">Select Category</option>';
            this.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        }

        // Populate skills
        const skillsContainer = document.getElementById('skillsContainer');
        if (skillsContainer) {
            skillsContainer.innerHTML = '';
            this.skills.forEach(skill => {
                const skillTag = document.createElement('div');
                skillTag.className = 'skill-tag';
                skillTag.innerHTML = `
                    <input type="checkbox" id="skill_${skill}" value="${skill}">
                    <label for="skill_${skill}">${skill}</label>
                `;
                skillsContainer.appendChild(skillTag);
            });
        }
    }

    // Handle file upload
    handleFileUpload(event) {
        const files = event.target.files;
        const filePreview = document.getElementById('filePreview');
        
        if (filePreview) {
            filePreview.innerHTML = '';
            
            Array.from(files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-preview-item';
                
                // Create preview based on file type
                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.style.maxWidth = '100px';
                    img.style.maxHeight = '100px';
                    fileItem.appendChild(img);
                }
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'file-info';
                fileInfo.innerHTML = `
                    <p><strong>${file.name}</strong></p>
                    <p>Size: ${this.formatFileSize(file.size)}</p>
                    <p>Type: ${file.type || 'Unknown'}</p>
                `;
                
                fileItem.appendChild(fileInfo);
                filePreview.appendChild(fileItem);
            });
        }
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Handle project form submission
    handleProjectSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const selectedSkills = Array.from(document.querySelectorAll('#skillsContainer input:checked'))
            .map(input => input.value);
        
        const project = {
            id: Date.now().toString(),
            title: formData.get('projectTitle'),
            description: formData.get('projectDescription'),
            category: formData.get('projectCategory'),
            skills: selectedSkills,
            projectUrl: formData.get('projectUrl'),
            githubUrl: formData.get('githubUrl'),
            status: formData.get('projectStatus') || 'completed',
            createdAt: new Date().toISOString(),
            files: this.processUploadedFiles(formData.getAll('projectFiles'))
        };

        this.addProject(project);
        this.hideAddProjectModal();
        this.displayProjects();
        
        // Show success message
        this.showNotification('Project added successfully!', 'success');
    }

    // Process uploaded files (simulate file storage)
    processUploadedFiles(files) {
        return files.map(file => ({
            name: file.name,
            size: file.size,
            type: file.type,
            url: URL.createObjectURL(file), // In real app, this would be uploaded to server
            uploadedAt: new Date().toISOString()
        }));
    }

    // Add project to portfolio
    addProject(project) {
        this.projects.unshift(project);
        this.saveProjects();
    }

    // Delete project
    deleteProject(projectId) {
        if (confirm('Are you sure you want to delete this project?')) {
            this.projects = this.projects.filter(p => p.id !== projectId);
            this.saveProjects();
            this.displayProjects();
            this.showNotification('Project deleted successfully!', 'success');
        }
    }

    // Edit project
    editProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (project) {
            this.populateEditForm(project);
            this.showAddProjectModal();
        }
    }

    // Populate edit form
    populateEditForm(project) {
        document.getElementById('projectTitle').value = project.title;
        document.getElementById('projectDescription').value = project.description;
        document.getElementById('projectCategory').value = project.category;
        document.getElementById('projectUrl').value = project.projectUrl || '';
        document.getElementById('githubUrl').value = project.githubUrl || '';
        document.getElementById('projectStatus').value = project.status;
        
        // Check skills
        project.skills.forEach(skill => {
            const checkbox = document.getElementById(`skill_${skill}`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // Display projects in the portfolio
    displayProjects() {
        const container = document.getElementById('portfolioContainer');
        if (!container) return;

        if (this.projects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>No Projects Yet</h3>
                    <p>Start building your portfolio by adding your first project!</p>
                    <button class="btn btn-primary" onclick="portfolioManager.showAddProjectModal()">
                        <i class="fas fa-plus"></i> Add Your First Project
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.projects.map(project => this.createProjectCard(project)).join('');
    }

    // Create project card HTML
    createProjectCard(project) {
        const skillTags = project.skills.map(skill => 
            `<span class="skill-tag">${skill}</span>`
        ).join('');

        const projectImages = project.files
            .filter(file => file.type.startsWith('image/'))
            .slice(0, 3)
            .map(file => `<img src="${file.url}" alt="Project image">`)
            .join('');

        return `
            <div class="project-card glass-card">
                <div class="project-images">
                    ${projectImages || '<div class="no-image"><i class="fas fa-image"></i></div>'}
                </div>
                <div class="project-content">
                    <div class="project-header">
                        <h3>${project.title}</h3>
                        <div class="project-actions">
                            <button class="btn-icon" onclick="portfolioManager.editProject('${project.id}')" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon" onclick="portfolioManager.deleteProject('${project.id}')" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <p class="project-description">${project.description}</p>
                    <div class="project-meta">
                        <span class="project-category">${project.category}</span>
                        <span class="project-status status-${project.status}">${project.status}</span>
                    </div>
                    <div class="project-skills">
                        ${skillTags}
                    </div>
                    <div class="project-links">
                        ${project.projectUrl ? `<a href="${project.projectUrl}" target="_blank" class="btn btn-sm">View Project</a>` : ''}
                        ${project.githubUrl ? `<a href="${project.githubUrl}" target="_blank" class="btn btn-sm">GitHub</a>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // Reset form
    resetForm() {
        const form = document.getElementById('projectForm');
        if (form) {
            form.reset();
            document.getElementById('filePreview').innerHTML = '';
            document.querySelectorAll('#skillsContainer input').forEach(input => input.checked = false);
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Get projects for public display
    getPublicProjects(userId) {
        const userProjects = localStorage.getItem(`portfolio_projects_${userId}`);
        return userProjects ? JSON.parse(userProjects) : [];
    }

    // Search projects
    searchProjects(query, category = '', skills = []) {
        return this.projects.filter(project => {
            const matchesQuery = !query || 
                project.title.toLowerCase().includes(query.toLowerCase()) ||
                project.description.toLowerCase().includes(query.toLowerCase());
            
            const matchesCategory = !category || project.category === category;
            
            const matchesSkills = skills.length === 0 || 
                skills.some(skill => project.skills.includes(skill));
            
            return matchesQuery && matchesCategory && matchesSkills;
        });
    }
}

// Initialize portfolio manager
let portfolioManager;
document.addEventListener('DOMContentLoaded', function() {
    portfolioManager = new PortfolioManager();
});
