import { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import DashboardLayout from '../layout/DashboardLayout'
import EmployerSearchDashboard from '../employer/EmployerSearchDashboard'
import {
  Briefcase,
  Search,
  Users,
  DollarSign,
  Calendar,
  MessageCircle,
  TrendingUp,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  MapPin,
  Filter
} from 'lucide-react'

const ClientDashboard = () => {
  const { user } = useAuth()
  const [activeView, setActiveView] = useState('overview')

  // Mock client data
  const clientStats = {
    activeProjects: 3,
    completedProjects: 15,
    totalSpent: 25000,
    savedFreelancers: 12,
    pendingProposals: 8,
    avgProjectCost: 1667,
    successRate: 94,
    avgRating: 4.7
  }

  const activeProjects = [
    {
      id: 1,
      title: 'E-commerce Website Development',
      freelancer: '<PERSON>',
      budget: 2500,
      deadline: '2024-02-15',
      progress: 75,
      status: 'in_progress',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 2,
      title: 'Mobile App UI Design',
      freelancer: 'Michael Chen',
      budget: 1800,
      deadline: '2024-02-20',
      progress: 45,
      status: 'in_progress',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 3,
      title: 'Content Writing',
      freelancer: 'Lisa Thompson',
      budget: 800,
      deadline: '2024-02-10',
      progress: 90,
      status: 'review',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'
    }
  ]

  const recommendedFreelancers = [
    {
      id: 1,
      name: 'David Kim',
      title: 'Mobile App Developer',
      rating: 4.9,
      hourlyRate: 85,
      skills: ['React Native', 'Flutter', 'iOS', 'Android'],
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      location: 'Seattle, WA',
      availability: 'available'
    },
    {
      id: 2,
      name: 'Emily Rodriguez',
      title: 'Digital Marketing Specialist',
      rating: 4.7,
      hourlyRate: 45,
      skills: ['SEO', 'Google Ads', 'Social Media', 'Content Strategy'],
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      location: 'Austin, TX',
      availability: 'available'
    }
  ]

  const navigation = [
    { id: 'overview', name: 'Overview', icon: TrendingUp, active: activeView === 'overview', onClick: () => setActiveView('overview') },
    { id: 'projects', name: 'My Projects', icon: Briefcase, active: activeView === 'projects', onClick: () => setActiveView('projects'), badge: '3' },
    { id: 'find-talent', name: 'Find Talent', icon: Search, active: activeView === 'find-talent', onClick: () => setActiveView('find-talent') },
    { id: 'saved', name: 'Saved Freelancers', icon: Users, active: activeView === 'saved', onClick: () => setActiveView('saved'), badge: '12' },
    { id: 'messages', name: 'Messages', icon: MessageCircle, active: activeView === 'messages', onClick: () => setActiveView('messages'), badge: '2' },
    { id: 'payments', name: 'Payments', icon: DollarSign, active: activeView === 'payments', onClick: () => setActiveView('payments') },
    { id: 'calendar', name: 'Calendar', icon: Calendar, active: activeView === 'calendar', onClick: () => setActiveView('calendar') }
  ]

  const StatCard = ({ title, value, change, icon: Icon, color = 'primary', suffix = '' }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">
            {typeof value === 'number' ? value.toLocaleString() : value}{suffix}
          </p>
          {change && (
            <p className={`text-sm mt-2 flex items-center ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              {change > 0 ? '+' : ''}{change}% this month
            </p>
          )}
        </div>
        <div className={`w-12 h-12 bg-${color}-100 rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  )

  const ProjectCard = ({ project }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start space-x-3">
          <img
            src={project.avatar}
            alt={project.freelancer}
            className="w-10 h-10 rounded-full object-cover"
          />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{project.title}</h3>
            <p className="text-sm text-gray-600">{project.freelancer}</p>
          </div>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          project.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
          project.status === 'review' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          {project.status.replace('_', ' ')}
        </span>
      </div>
      
      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Budget:</span>
          <span className="font-medium">${project.budget.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Deadline:</span>
          <span className="font-medium">{new Date(project.deadline).toLocaleDateString()}</span>
        </div>
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">Progress:</span>
            <span className="font-medium">{project.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-primary-500 h-2 rounded-full"
              style={{ width: `${project.progress}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )

  const FreelancerCard = ({ freelancer }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start space-x-4">
        <img
          src={freelancer.avatar}
          alt={freelancer.name}
          className="w-12 h-12 rounded-full object-cover"
        />
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{freelancer.name}</h3>
              <p className="text-sm text-primary-600">{freelancer.title}</p>
            </div>
            <div className="text-right">
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-500 mr-1" />
                <span className="text-sm font-medium">{freelancer.rating}</span>
              </div>
              <p className="text-sm text-gray-600">${freelancer.hourlyRate}/hr</p>
            </div>
          </div>
          
          <div className="flex items-center text-sm text-gray-600 mt-2">
            <MapPin className="w-4 h-4 mr-1" />
            {freelancer.location}
          </div>
          
          <div className="flex flex-wrap gap-2 mt-3">
            {freelancer.skills.slice(0, 3).map((skill) => (
              <span key={skill} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                {skill}
              </span>
            ))}
            {freelancer.skills.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                +{freelancer.skills.length - 3} more
              </span>
            )}
          </div>
          
          <div className="flex items-center justify-between mt-4">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              freelancer.availability === 'available' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {freelancer.availability}
            </span>
            <div className="space-x-2">
              <button className="btn btn-sm btn-outline">Save</button>
              <button className="btn btn-sm btn-primary">Contact</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-lg p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">Ready to start your next project?</h2>
        <p className="text-primary-100 mb-4">Find the perfect freelancer for your needs</p>
        <div className="flex space-x-3">
          <button className="btn bg-white text-primary-600 hover:bg-gray-100">
            <Plus className="w-4 h-4 mr-2" />
            Post a Project
          </button>
          <button className="btn btn-outline border-white text-white hover:bg-white hover:text-primary-600">
            <Search className="w-4 h-4 mr-2" />
            Browse Talent
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard title="Active Projects" value={clientStats.activeProjects} icon={Briefcase} />
        <StatCard title="Total Spent" value={clientStats.totalSpent} change={8.2} icon={DollarSign} color="green" suffix="$" />
        <StatCard title="Pending Proposals" value={clientStats.pendingProposals} icon={Clock} color="yellow" />
        <StatCard title="Success Rate" value={clientStats.successRate} icon={CheckCircle} color="purple" suffix="%" />
      </div>

      {/* Active Projects and Recommended Talent */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Projects */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Active Projects</h3>
            <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {activeProjects.slice(0, 2).map((project) => (
              <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{project.title}</h4>
                  <span className="text-sm text-gray-600">${project.budget}</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{project.freelancer}</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-500 h-2 rounded-full"
                    style={{ width: `${project.progress}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recommended Talent */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recommended Talent</h3>
            <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {recommendedFreelancers.map((freelancer) => (
              <div key={freelancer.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <img
                    src={freelancer.avatar}
                    alt={freelancer.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{freelancer.name}</h4>
                    <p className="text-sm text-gray-600">{freelancer.title}</p>
                    <div className="flex items-center mt-1">
                      <Star className="w-3 h-3 text-yellow-500 mr-1" />
                      <span className="text-xs text-gray-600">{freelancer.rating} • ${freelancer.hourlyRate}/hr</span>
                    </div>
                  </div>
                  <button className="btn btn-sm btn-outline">Contact</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeView) {
      case 'overview':
        return renderOverview()
      case 'projects':
        return <div className="space-y-4">{activeProjects.map(project => <ProjectCard key={project.id} project={project} />)}</div>
      case 'find-talent':
        return <EmployerSearchDashboard />
      case 'saved':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Saved freelancers interface...</p></div>
      case 'messages':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Messages interface...</p></div>
      case 'payments':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Payments interface...</p></div>
      case 'calendar':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Calendar interface...</p></div>
      default:
        return renderOverview()
    }
  }

  return (
    <DashboardLayout
      navigation={navigation}
      title="Client Dashboard"
      subtitle={`Welcome back, ${user?.firstName}!`}
    >
      {renderContent()}
    </DashboardLayout>
  )
}

export default ClientDashboard
