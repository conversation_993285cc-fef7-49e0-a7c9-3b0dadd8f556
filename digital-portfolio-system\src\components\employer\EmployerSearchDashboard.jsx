import { useState, useEffect } from 'react'
import { 
  Search, 
  Filter, 
  Bookmark, 
  MessageCircle, 
  Star, 
  MapPin, 
  Clock, 
  DollarSign,
  Users,
  TrendingUp,
  Eye,
  Heart,
  CheckCircle,
  Award
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import ContactModal from '../messaging/ContactModal'

const EmployerSearchDashboard = () => {
  const { user, isAuthenticated } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({
    skills: [],
    experience: '',
    hourlyRate: { min: '', max: '' },
    availability: '',
    location: '',
    rating: '',
    responseTime: '',
    projectType: ''
  })
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(false)
  const [selectedFreelancer, setSelectedFreelancer] = useState(null)
  const [showContactModal, setShowContactModal] = useState(false)
  const [savedFreelancers, setSavedFreelancers] = useState([])
  const [contactedFreelancers, setContactedFreelancers] = useState([])

  // Mock data for demonstration
  const mockFreelancers = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Senior Full-Stack Developer',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      location: 'San Francisco, CA',
      hourlyRate: 85,
      rating: 4.9,
      reviewCount: 127,
      responseTime: '2 hours',
      availability: 'Available',
      skills: ['React', 'Node.js', 'TypeScript', 'AWS', 'PostgreSQL'],
      description: 'Experienced full-stack developer with 6+ years building scalable web applications for startups and enterprises.',
      completedProjects: 89,
      successRate: 98,
      verified: true,
      lastActive: '2 hours ago',
      portfolio: [
        { title: 'E-commerce Platform', image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300' },
        { title: 'SaaS Dashboard', image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=300' }
      ]
    },
    {
      id: 2,
      name: 'Michael Chen',
      title: 'UI/UX Designer & Product Strategist',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      location: 'New York, NY',
      hourlyRate: 75,
      rating: 4.8,
      reviewCount: 94,
      responseTime: '1 hour',
      availability: 'Available',
      skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research', 'Design Systems'],
      description: 'Creative UI/UX designer with a passion for creating intuitive user experiences that drive business results.',
      completedProjects: 67,
      successRate: 96,
      verified: true,
      lastActive: '1 hour ago',
      portfolio: [
        { title: 'Mobile App Design', image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300' },
        { title: 'Website Redesign', image: 'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=300' }
      ]
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      title: 'Digital Marketing Specialist',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      location: 'Austin, TX',
      hourlyRate: 60,
      rating: 4.7,
      reviewCount: 156,
      responseTime: '4 hours',
      availability: 'Busy',
      skills: ['SEO', 'Google Ads', 'Content Marketing', 'Analytics', 'Social Media'],
      description: 'Results-driven digital marketer with proven track record of increasing online visibility and conversions.',
      completedProjects: 134,
      successRate: 94,
      verified: true,
      lastActive: '30 minutes ago',
      portfolio: [
        { title: 'SEO Campaign Results', image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300' },
        { title: 'Social Media Strategy', image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300' }
      ]
    }
  ]

  useEffect(() => {
    // Load saved data
    if (isAuthenticated) {
      const saved = JSON.parse(localStorage.getItem(`savedFreelancers_${user?.id}`) || '[]')
      const contacted = JSON.parse(localStorage.getItem(`contactedFreelancers_${user?.id}`) || '[]')
      setSavedFreelancers(saved)
      setContactedFreelancers(contacted)
    }
    
    // Initial load
    setResults(mockFreelancers)
  }, [isAuthenticated, user])

  const handleSearch = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      let filteredResults = mockFreelancers

      if (searchQuery) {
        filteredResults = filteredResults.filter(freelancer =>
          freelancer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          freelancer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          freelancer.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
        )
      }

      setResults(filteredResults)
      setLoading(false)
    }, 1000)
  }

  const handleSaveFreelancer = (freelancer) => {
    if (!isAuthenticated) {
      alert('Please log in to save freelancers')
      return
    }

    const isAlreadySaved = savedFreelancers.some(f => f.id === freelancer.id)
    let updatedSaved

    if (isAlreadySaved) {
      updatedSaved = savedFreelancers.filter(f => f.id !== freelancer.id)
    } else {
      updatedSaved = [...savedFreelancers, { ...freelancer, savedAt: new Date().toISOString() }]
    }

    setSavedFreelancers(updatedSaved)
    localStorage.setItem(`savedFreelancers_${user?.id}`, JSON.stringify(updatedSaved))
  }

  const handleContactFreelancer = (freelancer) => {
    setSelectedFreelancer(freelancer)
    setShowContactModal(true)
  }

  const handleSendMessage = (messageData) => {
    // Add to contacted list
    const updatedContacted = [...contactedFreelancers, {
      freelancerId: messageData.to.id,
      contactedAt: messageData.timestamp,
      subject: messageData.subject
    }]
    setContactedFreelancers(updatedContacted)
    localStorage.setItem(`contactedFreelancers_${user?.id}`, JSON.stringify(updatedContacted))
  }

  const isFreelancerSaved = (freelancerId) => {
    return savedFreelancers.some(f => f.id === freelancerId)
  }

  const isFreelancerContacted = (freelancerId) => {
    return contactedFreelancers.some(c => c.freelancerId === freelancerId)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Find Perfect Freelancers
          </h1>
          <p className="text-gray-600 text-lg">
            Discover and connect with top talent for your projects
          </p>
        </div>

        {/* Search Bar */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by skills, name, or expertise..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <button
              onClick={handleSearch}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Search
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-primary-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">{results.length}</p>
                <p className="text-sm text-gray-600">Available Freelancers</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <Bookmark className="w-8 h-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">{savedFreelancers.length}</p>
                <p className="text-sm text-gray-600">Saved Profiles</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <MessageCircle className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">{contactedFreelancers.length}</p>
                <p className="text-sm text-gray-600">Contacted</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">4.8</p>
                <p className="text-sm text-gray-600">Avg Rating</p>
              </div>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-gray-600 mt-4">Searching for freelancers...</p>
            </div>
          ) : (
            results.map((freelancer) => (
              <div key={freelancer.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Profile Info */}
                  <div className="flex-1">
                    <div className="flex items-start gap-4">
                      <img
                        src={freelancer.avatar}
                        alt={freelancer.name}
                        className="w-16 h-16 rounded-full object-cover"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-xl font-semibold text-gray-900">{freelancer.name}</h3>
                          {freelancer.verified && (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          )}
                        </div>
                        <p className="text-gray-600 mb-2">{freelancer.title}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-400 mr-1" />
                            <span>{freelancer.rating} ({freelancer.reviewCount})</span>
                          </div>
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            <span>{freelancer.location}</span>
                          </div>
                          <div className="flex items-center">
                            <DollarSign className="w-4 h-4 mr-1" />
                            <span>${freelancer.hourlyRate}/hr</span>
                          </div>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            <span>{freelancer.responseTime}</span>
                          </div>
                        </div>
                        <p className="text-gray-700 mb-3">{freelancer.description}</p>
                        <div className="flex flex-wrap gap-1">
                          {freelancer.skills.map((skill) => (
                            <span
                              key={skill}
                              className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Portfolio Preview */}
                  <div className="lg:w-64">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Work</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {freelancer.portfolio.slice(0, 2).map((item, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-20 object-cover rounded-lg"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                            <Eye className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="lg:w-48 flex lg:flex-col gap-3">
                    <button
                      onClick={() => handleSaveFreelancer(freelancer)}
                      className={`flex items-center justify-center px-4 py-2 border rounded-lg transition-colors ${
                        isFreelancerSaved(freelancer.id)
                          ? 'bg-purple-50 border-purple-200 text-purple-700'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Heart className={`w-4 h-4 mr-2 ${isFreelancerSaved(freelancer.id) ? 'fill-current' : ''}`} />
                      {isFreelancerSaved(freelancer.id) ? 'Saved' : 'Save'}
                    </button>
                    <button
                      onClick={() => handleContactFreelancer(freelancer)}
                      disabled={isFreelancerContacted(freelancer.id)}
                      className={`flex items-center justify-center px-4 py-2 rounded-lg transition-colors ${
                        isFreelancerContacted(freelancer.id)
                          ? 'bg-green-50 border border-green-200 text-green-700'
                          : 'bg-primary-600 text-white hover:bg-primary-700'
                      }`}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      {isFreelancerContacted(freelancer.id) ? 'Contacted' : 'Contact'}
                    </button>
                    <button className="flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                      <Eye className="w-4 h-4 mr-2" />
                      View Profile
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Contact Modal */}
      <ContactModal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
        freelancer={selectedFreelancer}
        onSendMessage={handleSendMessage}
      />
    </div>
  )
}

export default EmployerSearchDashboard
