// Dashboard-specific JavaScript

// Dashboard Data (Mock data for demonstration)
const dashboardData = {
    stats: {
        activeProjects: 12,
        totalEarnings: 4250,
        averageRating: 4.9,
        totalClients: 89
    },
    projects: [
        {
            id: 1,
            title: "E-commerce Website",
            description: "Modern online store design",
            status: "in-progress",
            deadline: "2024-12-15",
            value: 1200
        },
        {
            id: 2,
            title: "Mobile App UI",
            description: "iOS and Android app design",
            status: "completed",
            deadline: "2024-12-10",
            value: 800
        },
        {
            id: 3,
            title: "Brand Identity",
            description: "Logo and brand guidelines",
            status: "pending",
            deadline: "2024-12-20",
            value: 600
        }
    ],
    messages: [
        {
            id: 1,
            sender: "Hawinet Mekonen",
            avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face",
            message: "Great work on the website! Can we schedule a call to discuss...",
            time: "2 hours ago",
            unread: true
        },
        {
            id: 2,
            sender: "<PERSON><PERSON><PERSON>",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face",
            message: "The logo designs look amazing! I'd like to proceed with option 2.",
            time: "5 hours ago",
            unread: false
        }
    ],
    earnings: {
        daily: [450, 680, 320, 890, 560, 720, 950],
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    }
};

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    updateCharts();
});

function initializeDashboard() {
    console.log('Dashboard initialized');
    
    // Animate stats cards on load
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, index * 100);
    });
    
    // Animate dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    dashboardCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, (index + 4) * 100);
    });
}

function setupEventListeners() {
    // Chart control buttons
    const chartBtns = document.querySelectorAll('.chart-btn');
    chartBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            chartBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            updateChartData(this.textContent);
        });
    });
    
    // Quick action buttons
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            handleQuickAction(action);
        });
    });
    
    // Project items
    const projectItems = document.querySelectorAll('.project-item');
    projectItems.forEach(item => {
        item.addEventListener('click', function() {
            const projectTitle = this.querySelector('h4').textContent;
            showProjectDetails(projectTitle);
        });
    });
    
    // Message items
    const messageItems = document.querySelectorAll('.message-item');
    messageItems.forEach(item => {
        item.addEventListener('click', function() {
            const senderName = this.querySelector('h4').textContent;
            openMessage(senderName);
        });
    });
    
    // User menu
    const userMenu = document.querySelector('.user-menu');
    if (userMenu) {
        userMenu.addEventListener('click', function() {
            toggleUserDropdown();
        });
    }
}

function updateChartData(period) {
    console.log(`Updating chart for period: ${period}`);
    
    const chartBars = document.querySelectorAll('.chart-bar');
    let newData;
    
    switch(period) {
        case '7D':
            newData = [60, 80, 45, 90, 70, 85, 95];
            break;
        case '30D':
            newData = [40, 65, 55, 75, 60, 70, 80];
            break;
        case '90D':
            newData = [30, 50, 40, 60, 45, 55, 65];
            break;
        default:
            newData = [60, 80, 45, 90, 70, 85, 95];
    }
    
    chartBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.height = newData[index] + '%';
        }, index * 100);
    });
}

function handleQuickAction(action) {
    console.log(`Quick action: ${action}`);
    
    switch(action) {
        case 'New Project':
            showNewProjectModal();
            break;
        case 'Create Invoice':
            showInvoiceModal();
            break;
        case 'Schedule Meeting':
            showScheduleModal();
            break;
        case 'View Analytics':
            showAnalytics();
            break;
        default:
            alert(`${action} functionality coming soon!`);
    }
}

function showProjectDetails(projectTitle) {
    console.log(`Showing details for project: ${projectTitle}`);
    alert(`Project Details for: ${projectTitle}\n\nThis would open a detailed project view.`);
}

function openMessage(senderName) {
    console.log(`Opening message from: ${senderName}`);
    alert(`Opening conversation with: ${senderName}\n\nThis would open the messaging interface.`);
}

function toggleUserDropdown() {
    console.log('Toggling user dropdown');
    // This would show/hide a user dropdown menu
    alert('User menu would appear here with options like:\n- Profile Settings\n- Account\n- Logout');
}

function showNewProjectModal() {
    const modal = createModal('New Project', `
        <form id="newProjectForm">
            <div class="form-group">
                <label>Project Title</label>
                <input type="text" class="form-input" required>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea class="form-input" rows="3" required></textarea>
            </div>
            <div class="form-group">
                <label>Budget</label>
                <input type="number" class="form-input" required>
            </div>
            <div class="form-group">
                <label>Deadline</label>
                <input type="date" class="form-input" required>
            </div>
            <button type="submit" class="btn btn-primary w-full">Create Project</button>
        </form>
    `);
    
    document.body.appendChild(modal);
    modal.style.display = 'block';
}

function showInvoiceModal() {
    const modal = createModal('Create Invoice', `
        <form id="invoiceForm">
            <div class="form-group">
                <label>Client</label>
                <select class="form-input" required>
                    <option value="">Select Client</option>
                    <option value="hawinet">Hawinet Mekonen</option>
                    <option value="mahilet">Mahilet Ashenafi</option>
                    <option value="miskir">Miskir Tamire</option>
                </select>
            </div>
            <div class="form-group">
                <label>Project</label>
                <select class="form-input" required>
                    <option value="">Select Project</option>
                    <option value="ecommerce">E-commerce Website</option>
                    <option value="mobile">Mobile App UI</option>
                    <option value="brand">Brand Identity</option>
                </select>
            </div>
            <div class="form-group">
                <label>Amount</label>
                <input type="number" class="form-input" required>
            </div>
            <div class="form-group">
                <label>Due Date</label>
                <input type="date" class="form-input" required>
            </div>
            <button type="submit" class="btn btn-primary w-full">Create Invoice</button>
        </form>
    `);
    
    document.body.appendChild(modal);
    modal.style.display = 'block';
}

function showScheduleModal() {
    const modal = createModal('Schedule Meeting', `
        <form id="scheduleForm">
            <div class="form-group">
                <label>Meeting Title</label>
                <input type="text" class="form-input" required>
            </div>
            <div class="form-group">
                <label>Client</label>
                <select class="form-input" required>
                    <option value="">Select Client</option>
                    <option value="hawinet">Hawinet Mekonen</option>
                    <option value="mahilet">Mahilet Ashenafi</option>
                    <option value="miskir">Miskir Tamire</option>
                </select>
            </div>
            <div class="form-group">
                <label>Date</label>
                <input type="date" class="form-input" required>
            </div>
            <div class="form-group">
                <label>Time</label>
                <input type="time" class="form-input" required>
            </div>
            <div class="form-group">
                <label>Duration (minutes)</label>
                <select class="form-input" required>
                    <option value="30">30 minutes</option>
                    <option value="60">1 hour</option>
                    <option value="90">1.5 hours</option>
                    <option value="120">2 hours</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary w-full">Schedule Meeting</button>
        </form>
    `);
    
    document.body.appendChild(modal);
    modal.style.display = 'block';
}

function showAnalytics() {
    alert('Analytics Dashboard\n\nThis would show detailed analytics including:\n- Revenue trends\n- Client acquisition\n- Project completion rates\n- Performance metrics');
}

function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            <h2>${title}</h2>
            ${content}
        </div>
    `;
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    return modal;
}

function updateCharts() {
    // Animate chart bars on load
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.opacity = '1';
            bar.style.transform = 'scaleY(1)';
        }, index * 100);
    });
}

// Real-time updates simulation
function simulateRealTimeUpdates() {
    setInterval(() => {
        // Update stats randomly
        const statValues = document.querySelectorAll('.stat-content h3');
        statValues.forEach(stat => {
            const currentValue = parseInt(stat.textContent.replace(/[^0-9]/g, ''));
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
            const newValue = Math.max(0, currentValue + change);
            
            if (change !== 0) {
                stat.textContent = stat.textContent.replace(currentValue.toString(), newValue.toString());
                stat.parentElement.classList.add('animate-pulse-glow');
                setTimeout(() => {
                    stat.parentElement.classList.remove('animate-pulse-glow');
                }, 1000);
            }
        });
    }, 30000); // Update every 30 seconds
}

// Start real-time updates
// simulateRealTimeUpdates();

// Export functions for global use
window.handleQuickAction = handleQuickAction;
window.showProjectDetails = showProjectDetails;
window.openMessage = openMessage;

console.log('Dashboard JavaScript loaded successfully!');
