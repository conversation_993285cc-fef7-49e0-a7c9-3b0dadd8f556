<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Portfolio Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.html">
                    <i class="fas fa-briefcase"></i>
                    Portfolio Pro
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <i class="fas fa-envelope-check"></i>
                    <h1>Email Verification</h1>
                    <p>Verifying your email address...</p>
                </div>

                <div id="verificationContent" class="auth-content">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Please wait while we verify your email...</p>
                    </div>
                </div>

                <div class="auth-footer">
                    <p>Need help? <a href="mailto:<EMAIL>">Contact Support</a></p>
                </div>
            </div>
        </div>
    </main>

    <script src="scripts/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const contentDiv = document.getElementById('verificationContent');

            if (!token) {
                showError('Invalid verification link. Please check your email for the correct link.');
                return;
            }

            // Verify email with token
            authSystem.verifyEmail(token).then(result => {
                if (result.success) {
                    showSuccess(result.message);
                } else {
                    showError(result.message);
                }
            }).catch(error => {
                showError('An error occurred during verification. Please try again.');
            });
        });

        function showSuccess(message) {
            const contentDiv = document.getElementById('verificationContent');
            contentDiv.innerHTML = `
                <div class="verification-success">
                    <i class="fas fa-check-circle"></i>
                    <h2>Email Verified Successfully!</h2>
                    <p>${message}</p>
                    <div class="auth-actions">
                        <a href="index.html" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            Go to Login
                        </a>
                    </div>
                </div>
            `;
        }

        function showError(message) {
            const contentDiv = document.getElementById('verificationContent');
            contentDiv.innerHTML = `
                <div class="verification-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>Verification Failed</h2>
                    <p>${message}</p>
                    <div class="auth-actions">
                        <button onclick="resendVerification()" class="btn btn-secondary">
                            <i class="fas fa-envelope"></i>
                            Resend Verification Email
                        </button>
                        <a href="index.html" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Back to Home
                        </a>
                    </div>
                </div>
            `;
        }

        function resendVerification() {
            const email = prompt('Please enter your email address:');
            if (!email) return;

            authSystem.resendVerificationEmail(email).then(result => {
                if (result.success) {
                    alert(result.message);
                } else {
                    alert(result.message);
                }
            });
        }
    </script>

    <style>
        .auth-main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 1rem;
        }

        .auth-container {
            width: 100%;
            max-width: 500px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .auth-header i {
            font-size: 4rem;
            color: var(--accent-500);
            margin-bottom: 1rem;
        }

        .auth-header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .auth-header p {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .loading-spinner {
            padding: 2rem;
        }

        .loading-spinner i {
            font-size: 3rem;
            color: var(--accent-500);
            margin-bottom: 1rem;
        }

        .verification-success i,
        .verification-error i {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .verification-success i {
            color: var(--success-500);
        }

        .verification-error i {
            color: var(--error-500);
        }

        .verification-success h2,
        .verification-error h2 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .auth-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .auth-footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .auth-footer p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .auth-footer a {
            color: var(--accent-500);
            text-decoration: none;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .auth-card {
                padding: 2rem;
            }

            .auth-actions {
                flex-direction: column;
            }
        }
    </style>
</body>
</html>
