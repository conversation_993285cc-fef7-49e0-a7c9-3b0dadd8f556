import { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import DashboardLayout from '../layout/DashboardLayout'
import {
  User,
  Briefcase,
  DollarSign,
  Calendar,
  Award,
  MessageCircle,
  TrendingUp,
  Eye,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react'

const FreelancerDashboard = () => {
  const { user } = useAuth()
  const [activeView, setActiveView] = useState('overview')

  // Mock freelancer data
  const freelancerStats = {
    profileViews: 1247,
    activeProjects: 3,
    completedProjects: 89,
    totalEarnings: 45750,
    monthlyEarnings: 3200,
    rating: 4.9,
    responseTime: '2 hours',
    successRate: 98,
    profileCompletion: 95
  }

  const activeProjects = [
    {
      id: 1,
      title: 'E-commerce Website Development',
      client: 'TechCorp Inc.',
      budget: 2500,
      deadline: '2024-02-15',
      progress: 75,
      status: 'in_progress'
    },
    {
      id: 2,
      title: 'Mobile App UI Design',
      client: 'StartupXYZ',
      budget: 1800,
      deadline: '2024-02-20',
      progress: 45,
      status: 'in_progress'
    },
    {
      id: 3,
      title: 'Database Optimization',
      client: 'DataFlow Ltd.',
      budget: 1200,
      deadline: '2024-02-10',
      progress: 90,
      status: 'review'
    }
  ]

  const recentMessages = [
    { id: 1, client: 'TechCorp Inc.', message: 'Great progress on the website!', time: '2 hours ago', unread: true },
    { id: 2, client: 'StartupXYZ', message: 'Can we schedule a call?', time: '1 day ago', unread: false },
    { id: 3, client: 'DataFlow Ltd.', message: 'Please review the requirements', time: '2 days ago', unread: false }
  ]

  const navigation = [
    { id: 'overview', name: 'Overview', icon: TrendingUp, active: activeView === 'overview', onClick: () => setActiveView('overview') },
    { id: 'profile', name: 'Profile', icon: User, active: activeView === 'profile', onClick: () => setActiveView('profile') },
    { id: 'projects', name: 'Projects', icon: Briefcase, active: activeView === 'projects', onClick: () => setActiveView('projects'), badge: '3' },
    { id: 'earnings', name: 'Earnings', icon: DollarSign, active: activeView === 'earnings', onClick: () => setActiveView('earnings') },
    { id: 'calendar', name: 'Calendar', icon: Calendar, active: activeView === 'calendar', onClick: () => setActiveView('calendar') },
    { id: 'messages', name: 'Messages', icon: MessageCircle, active: activeView === 'messages', onClick: () => setActiveView('messages'), badge: '2' },
    { id: 'certifications', name: 'Certifications', icon: Award, active: activeView === 'certifications', onClick: () => setActiveView('certifications') }
  ]

  const StatCard = ({ title, value, change, icon: Icon, color = 'primary', suffix = '' }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">
            {typeof value === 'number' ? value.toLocaleString() : value}{suffix}
          </p>
          {change && (
            <p className={`text-sm mt-2 flex items-center ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              {change > 0 ? '+' : ''}{change}% this month
            </p>
          )}
        </div>
        <div className={`w-12 h-12 bg-${color}-100 rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  )

  const ProjectCard = ({ project }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{project.title}</h3>
          <p className="text-sm text-gray-600">{project.client}</p>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          project.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
          project.status === 'review' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          {project.status.replace('_', ' ')}
        </span>
      </div>
      
      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Budget:</span>
          <span className="font-medium">${project.budget.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Deadline:</span>
          <span className="font-medium">{new Date(project.deadline).toLocaleDateString()}</span>
        </div>
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">Progress:</span>
            <span className="font-medium">{project.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-primary-500 h-2 rounded-full"
              style={{ width: `${project.progress}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Profile Completion Alert */}
      {freelancerStats.profileCompletion < 100 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-yellow-600 mr-3" />
            <div className="flex-1">
              <p className="text-sm font-medium text-yellow-800">
                Complete your profile to get more visibility
              </p>
              <p className="text-sm text-yellow-700 mt-1">
                Your profile is {freelancerStats.profileCompletion}% complete
              </p>
            </div>
            <button className="btn btn-sm btn-outline text-yellow-700 border-yellow-300 hover:bg-yellow-100">
              Complete Profile
            </button>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard title="Profile Views" value={freelancerStats.profileViews} change={12.5} icon={Eye} />
        <StatCard title="Active Projects" value={freelancerStats.activeProjects} icon={Briefcase} color="blue" />
        <StatCard title="Monthly Earnings" value={freelancerStats.monthlyEarnings} change={8.2} icon={DollarSign} color="green" suffix="$" />
        <StatCard title="Success Rate" value={freelancerStats.successRate} icon={CheckCircle} color="purple" suffix="%" />
      </div>

      {/* Active Projects and Messages */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Projects */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Active Projects</h3>
            <button className="btn btn-sm btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              New Project
            </button>
          </div>
          <div className="space-y-4">
            {activeProjects.slice(0, 2).map((project) => (
              <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{project.title}</h4>
                  <span className="text-sm text-gray-600">${project.budget}</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{project.client}</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-500 h-2 rounded-full"
                    style={{ width: `${project.progress}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Messages */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Messages</h3>
          <div className="space-y-4">
            {recentMessages.map((message) => (
              <div key={message.id} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${message.unread ? 'bg-primary-500' : 'bg-gray-300'}`}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{message.client}</p>
                  <p className="text-sm text-gray-600 truncate">{message.message}</p>
                  <p className="text-xs text-gray-500">{message.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Star className="w-5 h-5 text-yellow-500 mr-1" />
              <span className="text-2xl font-bold text-gray-900">{freelancerStats.rating}</span>
            </div>
            <p className="text-sm text-gray-600">Average Rating</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-5 h-5 text-blue-500 mr-1" />
              <span className="text-2xl font-bold text-gray-900">{freelancerStats.responseTime}</span>
            </div>
            <p className="text-sm text-gray-600">Response Time</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="w-5 h-5 text-green-500 mr-1" />
              <span className="text-2xl font-bold text-gray-900">{freelancerStats.completedProjects}</span>
            </div>
            <p className="text-sm text-gray-600">Completed Projects</p>
          </div>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeView) {
      case 'overview':
        return renderOverview()
      case 'profile':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Profile management interface...</p></div>
      case 'projects':
        return <div className="space-y-4">{activeProjects.map(project => <ProjectCard key={project.id} project={project} />)}</div>
      case 'earnings':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Earnings analytics interface...</p></div>
      case 'calendar':
        return renderCalendar()
      case 'messages':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Messages interface...</p></div>
      case 'certifications':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Certifications interface...</p></div>
      default:
        return renderOverview()
    }
  }

  return (
    <DashboardLayout
      navigation={navigation}
      title="Freelancer Dashboard"
      subtitle={`Welcome back, ${user?.firstName}!`}
    >
      {renderContent()}
    </DashboardLayout>
  )
}

export default FreelancerDashboard
