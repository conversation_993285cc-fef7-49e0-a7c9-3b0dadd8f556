// Admin Dashboard JavaScript

// Admin Data
const adminData = {
    systemStats: {
        totalUsers: 1234,
        pendingApprovals: 23,
        activeProjects: 456,
        systemIssues: 3
    },
    pendingUsers: [
        {
            id: 'alex-rodriguez',
            name: '<PERSON>',
            email: '<EMAIL>',
            type: 'freelancer',
            registrationDate: '2024-12-12',
            skills: ['React', 'Node.js', 'MongoDB']
        },
        {
            id: 'emma-thompson',
            name: '<PERSON>',
            email: '<EMAIL>',
            type: 'client',
            registrationDate: '2024-12-11',
            company: 'TechStart Inc.'
        }
    ],
    systemIssues: [
        {
            id: 'db-timeout',
            title: 'Database Connection Timeout',
            description: 'Multiple users reporting slow loading times and connection errors',
            priority: 'critical',
            reportedTime: '2 hours ago',
            affectedUsers: 45
        },
        {
            id: 'payment-error',
            title: 'Payment Gateway Error',
            description: 'Some transactions failing during checkout process',
            priority: 'high',
            reportedTime: '4 hours ago',
            affectedUsers: 12
        }
    ],
    complaints: [
        {
            id: 'alex-complaint',
            user: '<PERSON>',
            email: '<EMAIL>',
            type: 'technical',
            title: 'Portfolio upload failing repeatedly',
            description: 'I\'ve been trying to upload my portfolio images for the past hour, but the upload keeps failing at 80%.',
            time: '2 hours ago'
        }
    ]
};

// Initialize Admin Dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminDashboard();
    setupAdminEventListeners();
    updateSystemStats();
    loadPendingUsers();
    loadSystemIssues();
});

function initializeAdminDashboard() {
    console.log('Admin Dashboard initialized');
    
    // Animate dashboard elements
    const dashboardCards = document.querySelectorAll('.dashboard-card, .stat-card');
    dashboardCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, index * 100);
    });
}

function setupAdminEventListeners() {
    // User management
    setupUserManagement();
    
    // System monitoring
    setupSystemMonitoring();
    
    // UI/UX management
    setupUIManagement();
    
    // Troubleshooting
    setupTroubleshooting();
}

function updateSystemStats() {
    const stats = adminData.systemStats;
    console.log('System stats updated:', stats);
    
    // Update notification badges
    updateNotificationBadges();
}

function updateNotificationBadges() {
    const pendingBadge = document.querySelector('.quick-action-btn .action-badge');
    if (pendingBadge) {
        pendingBadge.textContent = adminData.systemStats.pendingApprovals;
    }
}

function loadPendingUsers() {
    console.log('Loading pending users:', adminData.pendingUsers);
}

function loadSystemIssues() {
    console.log('Loading system issues:', adminData.systemIssues);
}

// User Management
function setupUserManagement() {
    // User search and filters
    const userFilters = document.querySelectorAll('.user-filters select, .user-filters input');
    userFilters.forEach(filter => {
        filter.addEventListener('change', filterUsers);
        filter.addEventListener('input', debounce(filterUsers, 300));
    });
    
    // Bulk actions
    const approveAllBtn = document.querySelector('.approval-actions .btn-success');
    if (approveAllBtn) {
        approveAllBtn.addEventListener('click', approveAllUsers);
    }
    
    const refreshBtn = document.querySelector('.approval-actions .btn-outline');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshPendingUsers);
    }
}

function filterUsers() {
    const userType = document.querySelector('.user-filters select').value;
    const searchTerm = document.querySelector('.user-filters input').value;
    
    console.log('Filtering users:', { type: userType, search: searchTerm });
    showNotification('Users filtered', 'info');
}

function approveUser(userId) {
    console.log('Approving user:', userId);
    
    showNotification('Approving user...', 'info');
    
    setTimeout(() => {
        // Remove user from pending list
        const userItem = document.querySelector(`[data-user-id="${userId}"]`);
        if (userItem) {
            userItem.remove();
        }
        
        // Update stats
        adminData.systemStats.pendingApprovals--;
        updateNotificationBadges();
        
        showNotification('User approved successfully!', 'success');
    }, 1000);
}

function rejectUser(userId) {
    console.log('Rejecting user:', userId);
    
    if (confirm('Are you sure you want to reject this user?')) {
        showNotification('Rejecting user...', 'info');
        
        setTimeout(() => {
            const userItem = document.querySelector(`[data-user-id="${userId}"]`);
            if (userItem) {
                userItem.remove();
            }
            
            adminData.systemStats.pendingApprovals--;
            updateNotificationBadges();
            
            showNotification('User rejected', 'warning');
        }, 1000);
    }
}

function viewUserDetails(userId) {
    console.log('Viewing user details:', userId);
    showNotification(`Opening ${userId} details...`, 'info');
}

function approveAllUsers() {
    if (confirm('Are you sure you want to approve all pending users?')) {
        showNotification('Approving all users...', 'info');
        
        setTimeout(() => {
            document.querySelectorAll('.pending-user-item').forEach(item => {
                item.remove();
            });
            
            adminData.systemStats.pendingApprovals = 0;
            updateNotificationBadges();
            
            showNotification('All users approved successfully!', 'success');
        }, 2000);
    }
}

function refreshPendingUsers() {
    showNotification('Refreshing pending users...', 'info');
    
    setTimeout(() => {
        showNotification('Pending users refreshed', 'success');
    }, 1000);
}

function suspendUser(userId) {
    console.log('Suspending user:', userId);
    
    if (confirm('Are you sure you want to suspend this user?')) {
        showNotification('Suspending user...', 'info');
        
        setTimeout(() => {
            showNotification('User suspended', 'warning');
        }, 1000);
    }
}

function deleteUser(userId) {
    console.log('Deleting user:', userId);
    
    if (confirm('Are you sure you want to permanently delete this user? This action cannot be undone.')) {
        showNotification('Deleting user...', 'info');
        
        setTimeout(() => {
            showNotification('User deleted', 'error');
        }, 1500);
    }
}

function viewUser(userId) {
    console.log('Viewing user:', userId);
    showNotification(`Opening ${userId} profile...`, 'info');
}

// System Monitoring
function setupSystemMonitoring() {
    // System health monitoring
    setInterval(updateSystemHealth, 30000); // Update every 30 seconds
    
    // Quick actions
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            handleQuickAction(action);
        });
    });
}

function updateSystemHealth() {
    // Simulate system health updates
    console.log('System health updated');
}

function handleQuickAction(action) {
    console.log('Quick action:', action);
    
    switch (action) {
        case 'Approve Users':
            approveUsers();
            break;
        case 'Review Reports':
            reviewReports();
            break;
        case 'Maintenance':
            systemMaintenance();
            break;
        case 'Backup System':
            backupSystem();
            break;
        default:
            showNotification(`${action} functionality`, 'info');
    }
}

function approveUsers() {
    showNotification('Opening user approval interface...', 'info');
    switchTab('users');
}

function reviewReports() {
    showNotification('Opening reports review...', 'info');
}

function systemMaintenance() {
    if (confirm('Are you sure you want to put the system in maintenance mode?')) {
        showNotification('Enabling maintenance mode...', 'warning');
        
        setTimeout(() => {
            showNotification('System is now in maintenance mode', 'warning');
        }, 2000);
    }
}

function backupSystem() {
    showNotification('Starting system backup...', 'info');
    
    setTimeout(() => {
        showNotification('System backup completed successfully!', 'success');
    }, 5000);
}

function generateReport() {
    showNotification('Generating system report...', 'info');
    
    setTimeout(() => {
        showNotification('System report generated successfully!', 'success');
    }, 3000);
}

// Troubleshooting
function setupTroubleshooting() {
    // Issue management
    const issueFilters = document.querySelectorAll('.issue-filters select');
    issueFilters.forEach(filter => {
        filter.addEventListener('change', filterIssues);
    });
    
    // Complaint management
    const complaintFilters = document.querySelectorAll('.complaint-filters select');
    complaintFilters.forEach(filter => {
        filter.addEventListener('change', filterComplaints);
    });
}

function filterIssues() {
    const priority = document.querySelector('.issue-filters select').value;
    console.log('Filtering issues by priority:', priority);
    showNotification('Issues filtered', 'info');
}

function filterComplaints() {
    const type = document.querySelector('.complaint-filters select').value;
    console.log('Filtering complaints by type:', type);
    showNotification('Complaints filtered', 'info');
}

function runSystemDiagnostics() {
    showNotification('Running system diagnostics...', 'info');
    
    setTimeout(() => {
        showNotification('System diagnostics completed', 'success');
    }, 3000);
}

function runFullDiagnostics() {
    showNotification('Running full system diagnostics...', 'info');
    
    setTimeout(() => {
        showNotification('Full diagnostics completed - 2 issues found', 'warning');
    }, 5000);
}

function resolveIssue(issueId) {
    console.log('Resolving issue:', issueId);
    
    showNotification('Resolving issue...', 'info');
    
    setTimeout(() => {
        const issueItem = document.querySelector(`[data-issue-id="${issueId}"]`);
        if (issueItem) {
            issueItem.remove();
        }
        
        adminData.systemStats.systemIssues--;
        
        showNotification('Issue resolved successfully!', 'success');
    }, 2000);
}

function viewIssueDetails(issueId) {
    console.log('Viewing issue details:', issueId);
    showNotification(`Opening ${issueId} details...`, 'info');
}

function respondToComplaint(complaintId) {
    console.log('Responding to complaint:', complaintId);
    showNotification('Opening response interface...', 'info');
}

function resolveComplaint(complaintId) {
    console.log('Resolving complaint:', complaintId);
    
    showNotification('Resolving complaint...', 'info');
    
    setTimeout(() => {
        showNotification('Complaint resolved successfully!', 'success');
    }, 1500);
}

function escalateComplaint(complaintId) {
    console.log('Escalating complaint:', complaintId);
    showNotification('Complaint escalated to senior support', 'warning');
}

// UI/UX Management
function setupUIManagement() {
    // Theme management
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            colorOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            
            const theme = this.getAttribute('data-theme');
            applyTheme(theme);
        });
    });
    
    // Feature toggles
    const featureToggles = document.querySelectorAll('.feature-item .toggle-switch input');
    featureToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const featureName = this.closest('.feature-item').querySelector('h4, h5').textContent;
            toggleFeature(featureName, this.checked);
        });
    });
}

function applyTheme(theme) {
    console.log('Applying theme:', theme);
    showNotification(`Applied ${theme} theme`, 'success');
}

function toggleFeature(featureName, enabled) {
    console.log('Feature toggle:', featureName, enabled);
    showNotification(`${featureName} ${enabled ? 'enabled' : 'disabled'}`, 'info');
}

function previewChanges() {
    showNotification('Opening preview mode...', 'info');
}

function saveContentChanges() {
    showNotification('Saving content changes...', 'info');
    
    setTimeout(() => {
        showNotification('Content changes saved successfully!', 'success');
    }, 1500);
}

function editSection(sectionName) {
    console.log('Editing section:', sectionName);
    showNotification(`Opening ${sectionName} editor...`, 'info');
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Notification function (if not already defined)
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Export functions for global use
window.approveUser = approveUser;
window.rejectUser = rejectUser;
window.viewUserDetails = viewUserDetails;
window.approveAllUsers = approveAllUsers;
window.refreshPendingUsers = refreshPendingUsers;
window.suspendUser = suspendUser;
window.deleteUser = deleteUser;
window.viewUser = viewUser;
window.generateReport = generateReport;
window.runSystemDiagnostics = runSystemDiagnostics;
window.runFullDiagnostics = runFullDiagnostics;
window.resolveIssue = resolveIssue;
window.viewIssueDetails = viewIssueDetails;
window.respondToComplaint = respondToComplaint;
window.resolveComplaint = resolveComplaint;
window.escalateComplaint = escalateComplaint;
window.previewChanges = previewChanges;
window.saveContentChanges = saveContentChanges;
window.editSection = editSection;

console.log('Admin Dashboard JavaScript loaded successfully!');
