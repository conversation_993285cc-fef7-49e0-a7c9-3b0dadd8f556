import { useState } from 'react'
import { Menu, X, User, Search, Briefcase, BarChart3, Calendar, Award, LogOut } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

const Header = ({ currentView, onViewChange, onAuthAction }) => {
  const { user, isAuthenticated, logout } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const handleDashboardClick = () => {
    if (isAuthenticated) {
      onViewChange('dashboard')
    } else {
      onAuthAction('login')
    }
  }

  const handleLogout = () => {
    logout()
    onViewChange('home')
  }

  const navigation = [
    { name: 'Home', id: 'home', icon: Briefcase },
    { name: 'Browse Freelancers', id: 'browse', icon: Search },
    { name: 'Create Profile', id: 'profile', icon: User },
    { name: 'Analytics', id: 'analytics', icon: BarChart3 },
    { name: 'Calendar', id: 'calendar', icon: Calendar },
    { name: 'Certifications', id: 'certifications', icon: Award }
  ]

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-lg">
      <div className="container">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-lg">f</span>
                  </div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    fiverr<span className="text-green-500">.</span>
                  </h1>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:block">
            <div className="flex items-center space-x-1">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => onViewChange(item.id)}
                    className={`group flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      currentView === item.id
                        ? 'text-green-600 bg-green-50 border border-green-200'
                        : 'text-gray-700 hover:text-green-600 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {item.name}
                  </button>
                )
              })}
            </div>
          </nav>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-3">
            {isAuthenticated ? (
              <>
                <button
                  onClick={handleDashboardClick}
                  className="px-4 py-2 text-gray-700 hover:text-green-600 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200"
                >
                  Dashboard
                </button>
                <div className="flex items-center space-x-3 px-3 py-2 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="relative">
                    <img
                      src={user?.avatar}
                      alt={`${user?.firstName} ${user?.lastName}`}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <span className="text-gray-700 text-sm font-medium">
                    {user?.firstName} {user?.lastName}
                  </span>
                  <button
                    onClick={handleLogout}
                    className="p-1 text-gray-500 hover:text-red-600 rounded-lg transition-all duration-200"
                    title="Logout"
                  >
                    <LogOut className="w-4 h-4" />
                  </button>
                </div>
              </>
            ) : (
              <>
                <button
                  onClick={() => onAuthAction && onAuthAction('login')}
                  className="px-4 py-2 text-gray-700 hover:text-green-600 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200"
                >
                  Sign In
                </button>
                <button
                  onClick={() => onAuthAction && onAuthAction('register')}
                  className="px-6 py-2 bg-green-500 hover:bg-green-600 text-white font-semibold rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                >
                  Join
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-800"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t border-gray-700 bg-gray-900">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      onViewChange(item.id)
                      setIsMenuOpen(false)
                    }}
                    className={`flex items-center gap-2 w-full px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      currentView === item.id
                        ? 'text-white bg-accent-600'
                        : 'text-gray-300 hover:text-white hover:bg-gray-800'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {item.name}
                  </button>
                )
              })}
              <div className="pt-4 space-y-2">
                <button
                  onClick={() => {
                    onAuthAction && onAuthAction('login')
                    setIsMenuOpen(false)
                  }}
                  className="btn btn-outline w-full"
                >
                  Sign In
                </button>
                <button
                  onClick={() => {
                    onAuthAction && onAuthAction('register')
                    setIsMenuOpen(false)
                  }}
                  className="btn btn-primary w-full"
                >
                  Get Started
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
