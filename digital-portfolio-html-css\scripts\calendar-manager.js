// Calendar Manager - Comprehensive calendar system for scheduling and event management
class CalendarManager {
    constructor() {
        this.events = [];
        this.currentDate = new Date();
        this.selectedDate = null;
        this.viewMode = 'month'; // month, week, day
        this.eventTypes = {
            meeting: { color: '#3b82f6', icon: 'fas fa-users' },
            deadline: { color: '#ef4444', icon: 'fas fa-clock' },
            reminder: { color: '#f59e0b', icon: 'fas fa-bell' },
            personal: { color: '#10b981', icon: 'fas fa-user' }
        };
        this.init();
    }

    init() {
        this.loadEvents();
        this.setupEventListeners();
        this.renderCalendar();
    }

    // Load events from localStorage
    loadEvents() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return;

        const userEvents = localStorage.getItem(`calendar_events_${currentUser.id}`);
        this.events = userEvents ? JSON.parse(userEvents) : this.createSampleEvents();
        this.saveEvents(); // Save sample events if none exist
    }

    // Save events to localStorage
    saveEvents() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return;

        localStorage.setItem(`calendar_events_${currentUser.id}`, JSON.stringify(this.events));
    }

    // Create sample events for demonstration
    createSampleEvents() {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        
        const nextWeek = new Date(today);
        nextWeek.setDate(today.getDate() + 7);

        return [
            {
                id: 'event_' + Date.now() + '_1',
                title: 'Client Meeting - Hawinet Mekonen',
                description: 'Discuss website redesign project requirements',
                startDate: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 14, 0),
                endDate: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 15, 0),
                type: 'meeting',
                attendees: ['<EMAIL>'],
                location: 'Zoom Meeting',
                createdAt: new Date().toISOString()
            },
            {
                id: 'event_' + Date.now() + '_2',
                title: 'Project Deadline - Mobile App UI',
                description: 'Final delivery for Mahilet Ashenafi mobile app project',
                startDate: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 23, 59),
                endDate: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 23, 59),
                type: 'deadline',
                priority: 'high',
                createdAt: new Date().toISOString()
            },
            {
                id: 'event_' + Date.now() + '_3',
                title: 'Follow-up Call - Miskir Tamire',
                description: 'Check project progress and gather feedback',
                startDate: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 10, 30),
                endDate: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 11, 0),
                type: 'meeting',
                attendees: ['<EMAIL>'],
                createdAt: new Date().toISOString()
            }
        ];
    }

    // Setup event listeners
    setupEventListeners() {
        // Calendar navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.calendar-prev')) {
                this.navigateCalendar(-1);
            } else if (e.target.matches('.calendar-next')) {
                this.navigateCalendar(1);
            } else if (e.target.matches('.calendar-today')) {
                this.goToToday();
            }
        });

        // View mode buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-mode-btn')) {
                const mode = e.target.dataset.mode;
                this.changeViewMode(mode);
            }
        });

        // Date cell clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.calendar-date')) {
                const date = new Date(e.target.dataset.date);
                this.selectDate(date);
            }
        });

        // Event clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.calendar-event')) {
                const eventId = e.target.closest('.calendar-event').dataset.eventId;
                this.showEventDetails(eventId);
            }
        });

        // Add event button
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-event-btn') || e.target.closest('.add-event-btn')) {
                this.showAddEventModal();
            }
        });

        // Event form submission
        const eventForm = document.getElementById('eventForm');
        if (eventForm) {
            eventForm.addEventListener('submit', (e) => this.handleEventSubmit(e));
        }
    }

    // Render the calendar
    renderCalendar() {
        const container = document.getElementById('calendarContainer');
        if (!container) return;

        const calendarHTML = `
            <div class="calendar-header">
                <div class="calendar-navigation">
                    <button class="btn btn-icon calendar-prev">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h2 class="calendar-title">${this.getCalendarTitle()}</h2>
                    <button class="btn btn-icon calendar-next">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="calendar-controls">
                    <button class="btn btn-sm calendar-today">Today</button>
                    <div class="view-mode-buttons">
                        <button class="view-mode-btn ${this.viewMode === 'month' ? 'active' : ''}" data-mode="month">Month</button>
                        <button class="view-mode-btn ${this.viewMode === 'week' ? 'active' : ''}" data-mode="week">Week</button>
                        <button class="view-mode-btn ${this.viewMode === 'day' ? 'active' : ''}" data-mode="day">Day</button>
                    </div>
                    <button class="btn btn-primary add-event-btn">
                        <i class="fas fa-plus"></i> Add Event
                    </button>
                </div>
            </div>
            <div class="calendar-body">
                ${this.renderCalendarView()}
            </div>
        `;

        container.innerHTML = calendarHTML;
    }

    // Get calendar title based on current date and view mode
    getCalendarTitle() {
        const options = { year: 'numeric', month: 'long' };
        if (this.viewMode === 'day') {
            options.day = 'numeric';
            options.weekday = 'long';
        } else if (this.viewMode === 'week') {
            const weekStart = this.getWeekStart(this.currentDate);
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
        }
        return this.currentDate.toLocaleDateString('en-US', options);
    }

    // Render calendar view based on current view mode
    renderCalendarView() {
        switch (this.viewMode) {
            case 'month':
                return this.renderMonthView();
            case 'week':
                return this.renderWeekView();
            case 'day':
                return this.renderDayView();
            default:
                return this.renderMonthView();
        }
    }

    // Render month view
    renderMonthView() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const weeks = [];
        const currentWeek = [];
        
        for (let i = 0; i < 42; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            currentWeek.push(date);
            
            if (currentWeek.length === 7) {
                weeks.push([...currentWeek]);
                currentWeek.length = 0;
            }
        }

        const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        
        return `
            <div class="calendar-month">
                <div class="calendar-weekdays">
                    ${weekDays.map(day => `<div class="weekday">${day}</div>`).join('')}
                </div>
                <div class="calendar-weeks">
                    ${weeks.map(week => `
                        <div class="calendar-week">
                            ${week.map(date => this.renderDateCell(date, month)).join('')}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Render date cell
    renderDateCell(date, currentMonth) {
        const isCurrentMonth = date.getMonth() === currentMonth;
        const isToday = this.isToday(date);
        const isSelected = this.selectedDate && this.isSameDay(date, this.selectedDate);
        const dayEvents = this.getEventsForDate(date);

        const classes = [
            'calendar-date',
            !isCurrentMonth ? 'other-month' : '',
            isToday ? 'today' : '',
            isSelected ? 'selected' : '',
            dayEvents.length > 0 ? 'has-events' : ''
        ].filter(Boolean).join(' ');

        return `
            <div class="${classes}" data-date="${date.toISOString()}">
                <div class="date-number">${date.getDate()}</div>
                <div class="date-events">
                    ${dayEvents.slice(0, 3).map(event => `
                        <div class="calendar-event" data-event-id="${event.id}" style="background-color: ${this.eventTypes[event.type].color}">
                            <span class="event-title">${event.title}</span>
                        </div>
                    `).join('')}
                    ${dayEvents.length > 3 ? `<div class="more-events">+${dayEvents.length - 3} more</div>` : ''}
                </div>
            </div>
        `;
    }

    // Get events for a specific date
    getEventsForDate(date) {
        return this.events.filter(event => {
            const eventDate = new Date(event.startDate);
            return this.isSameDay(eventDate, date);
        });
    }

    // Utility functions
    isToday(date) {
        const today = new Date();
        return this.isSameDay(date, today);
    }

    isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    getWeekStart(date) {
        const start = new Date(date);
        start.setDate(date.getDate() - date.getDay());
        return start;
    }

    // Navigation functions
    navigateCalendar(direction) {
        if (this.viewMode === 'month') {
            this.currentDate.setMonth(this.currentDate.getMonth() + direction);
        } else if (this.viewMode === 'week') {
            this.currentDate.setDate(this.currentDate.getDate() + (direction * 7));
        } else if (this.viewMode === 'day') {
            this.currentDate.setDate(this.currentDate.getDate() + direction);
        }
        this.renderCalendar();
    }

    goToToday() {
        this.currentDate = new Date();
        this.renderCalendar();
    }

    changeViewMode(mode) {
        this.viewMode = mode;
        this.renderCalendar();
    }

    selectDate(date) {
        this.selectedDate = date;
        this.renderCalendar();
        this.showDayEvents(date);
    }

    // Show events for selected day
    showDayEvents(date) {
        const events = this.getEventsForDate(date);
        const sidebar = document.getElementById('calendarSidebar');
        
        if (sidebar) {
            sidebar.innerHTML = `
                <div class="sidebar-header">
                    <h3>${date.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</h3>
                    <button class="btn btn-sm btn-primary add-event-btn">
                        <i class="fas fa-plus"></i> Add Event
                    </button>
                </div>
                <div class="sidebar-events">
                    ${events.length === 0 ? 
                        '<div class="no-events">No events scheduled</div>' :
                        events.map(event => this.renderEventItem(event)).join('')
                    }
                </div>
            `;
        }
    }

    // Render event item
    renderEventItem(event) {
        const startTime = new Date(event.startDate).toLocaleTimeString('en-US', { 
            hour: 'numeric', 
            minute: '2-digit',
            hour12: true 
        });
        
        const endTime = new Date(event.endDate).toLocaleTimeString('en-US', { 
            hour: 'numeric', 
            minute: '2-digit',
            hour12: true 
        });

        return `
            <div class="event-item" data-event-id="${event.id}">
                <div class="event-time" style="color: ${this.eventTypes[event.type].color}">
                    <i class="${this.eventTypes[event.type].icon}"></i>
                    ${startTime} - ${endTime}
                </div>
                <div class="event-details">
                    <h4 class="event-title">${event.title}</h4>
                    ${event.description ? `<p class="event-description">${event.description}</p>` : ''}
                    ${event.location ? `<div class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</div>` : ''}
                </div>
                <div class="event-actions">
                    <button class="btn-icon" onclick="calendarManager.editEvent('${event.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="calendarManager.deleteEvent('${event.id}')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
}

    // Show add event modal
    showAddEventModal(selectedDate = null) {
        const modal = document.getElementById('addEventModal');
        if (modal) {
            modal.style.display = 'flex';

            // Pre-fill date if provided
            if (selectedDate || this.selectedDate) {
                const date = selectedDate || this.selectedDate;
                const dateInput = document.getElementById('eventDate');
                if (dateInput) {
                    dateInput.value = date.toISOString().split('T')[0];
                }
            }
        }
    }

    // Hide add event modal
    hideAddEventModal() {
        const modal = document.getElementById('addEventModal');
        if (modal) {
            modal.style.display = 'none';
            this.resetEventForm();
        }
    }

    // Handle event form submission
    handleEventSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const startDateTime = new Date(`${formData.get('eventDate')}T${formData.get('startTime')}`);
        const endDateTime = new Date(`${formData.get('eventDate')}T${formData.get('endTime')}`);

        const event = {
            id: 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            title: formData.get('eventTitle'),
            description: formData.get('eventDescription') || '',
            startDate: startDateTime,
            endDate: endDateTime,
            type: formData.get('eventType'),
            location: formData.get('eventLocation') || '',
            attendees: formData.get('eventAttendees') ?
                formData.get('eventAttendees').split(',').map(email => email.trim()) : [],
            priority: formData.get('eventPriority') || 'medium',
            createdAt: new Date().toISOString()
        };

        this.addEvent(event);
        this.hideAddEventModal();
        this.showNotification('Event added successfully!', 'success');
    }

    // Add event
    addEvent(event) {
        this.events.push(event);
        this.saveEvents();
        this.renderCalendar();

        // Update sidebar if date is selected
        if (this.selectedDate && this.isSameDay(new Date(event.startDate), this.selectedDate)) {
            this.showDayEvents(this.selectedDate);
        }
    }

    // Edit event
    editEvent(eventId) {
        const event = this.events.find(e => e.id === eventId);
        if (event) {
            this.populateEventForm(event);
            this.showAddEventModal();
        }
    }

    // Delete event
    deleteEvent(eventId) {
        if (confirm('Are you sure you want to delete this event?')) {
            this.events = this.events.filter(e => e.id !== eventId);
            this.saveEvents();
            this.renderCalendar();

            // Update sidebar if date is selected
            if (this.selectedDate) {
                this.showDayEvents(this.selectedDate);
            }

            this.showNotification('Event deleted successfully!', 'success');
        }
    }

    // Show event details
    showEventDetails(eventId) {
        const event = this.events.find(e => e.id === eventId);
        if (event) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'flex';

            const startTime = new Date(event.startDate).toLocaleString();
            const endTime = new Date(event.endDate).toLocaleString();

            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="${this.eventTypes[event.type].icon}"></i> ${event.title}</h2>
                        <button class="close-btn" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="event-detail-item">
                            <strong>Time:</strong> ${startTime} - ${endTime}
                        </div>
                        ${event.description ? `
                            <div class="event-detail-item">
                                <strong>Description:</strong> ${event.description}
                            </div>
                        ` : ''}
                        ${event.location ? `
                            <div class="event-detail-item">
                                <strong>Location:</strong> ${event.location}
                            </div>
                        ` : ''}
                        ${event.attendees && event.attendees.length > 0 ? `
                            <div class="event-detail-item">
                                <strong>Attendees:</strong> ${event.attendees.join(', ')}
                            </div>
                        ` : ''}
                        <div class="event-detail-item">
                            <strong>Type:</strong> <span style="color: ${this.eventTypes[event.type].color}">${event.type}</span>
                        </div>
                        ${event.priority ? `
                            <div class="event-detail-item">
                                <strong>Priority:</strong> ${event.priority}
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="calendarManager.editEvent('${event.id}'); this.closest('.modal').remove();">
                            <i class="fas fa-edit"></i> Edit Event
                        </button>
                        <button class="btn btn-danger" onclick="calendarManager.deleteEvent('${event.id}'); this.closest('.modal').remove();">
                            <i class="fas fa-trash"></i> Delete Event
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }
    }

    // Populate event form for editing
    populateEventForm(event) {
        const startDate = new Date(event.startDate);
        const endDate = new Date(event.endDate);

        document.getElementById('eventTitle').value = event.title;
        document.getElementById('eventDescription').value = event.description || '';
        document.getElementById('eventDate').value = startDate.toISOString().split('T')[0];
        document.getElementById('startTime').value = startDate.toTimeString().slice(0, 5);
        document.getElementById('endTime').value = endDate.toTimeString().slice(0, 5);
        document.getElementById('eventType').value = event.type;
        document.getElementById('eventLocation').value = event.location || '';
        document.getElementById('eventAttendees').value = event.attendees ? event.attendees.join(', ') : '';
        document.getElementById('eventPriority').value = event.priority || 'medium';
    }

    // Reset event form
    resetEventForm() {
        const form = document.getElementById('eventForm');
        if (form) {
            form.reset();

            // Set default date to today
            const today = new Date();
            document.getElementById('eventDate').value = today.toISOString().split('T')[0];
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Get upcoming events
    getUpcomingEvents(days = 7) {
        const now = new Date();
        const futureDate = new Date();
        futureDate.setDate(now.getDate() + days);

        return this.events
            .filter(event => {
                const eventDate = new Date(event.startDate);
                return eventDate >= now && eventDate <= futureDate;
            })
            .sort((a, b) => new Date(a.startDate) - new Date(b.startDate));
    }

    // Export calendar data
    exportCalendar(format = 'ics') {
        if (format === 'ics') {
            this.exportToICS();
        } else if (format === 'json') {
            this.exportToJSON();
        }
    }

    // Export to ICS format (iCalendar)
    exportToICS() {
        let icsContent = [
            'BEGIN:VCALENDAR',
            'VERSION:2.0',
            'PRODID:-//PortfolioPro//Calendar//EN',
            'CALSCALE:GREGORIAN'
        ];

        this.events.forEach(event => {
            const startDate = new Date(event.startDate);
            const endDate = new Date(event.endDate);

            icsContent.push(
                'BEGIN:VEVENT',
                `UID:${event.id}@portfoliopro.com`,
                `DTSTART:${this.formatDateForICS(startDate)}`,
                `DTEND:${this.formatDateForICS(endDate)}`,
                `SUMMARY:${event.title}`,
                `DESCRIPTION:${event.description || ''}`,
                `LOCATION:${event.location || ''}`,
                `CREATED:${this.formatDateForICS(new Date(event.createdAt))}`,
                'END:VEVENT'
            );
        });

        icsContent.push('END:VCALENDAR');

        const blob = new Blob([icsContent.join('\r\n')], { type: 'text/calendar' });
        this.downloadFile(blob, 'portfolio-calendar.ics');
    }

    // Export to JSON format
    exportToJSON() {
        const data = {
            exportDate: new Date().toISOString(),
            events: this.events
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        this.downloadFile(blob, 'portfolio-calendar.json');
    }

    // Format date for ICS
    formatDateForICS(date) {
        return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    }

    // Download file utility
    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Initialize calendar manager
let calendarManager;
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('calendarContainer')) {
        calendarManager = new CalendarManager();
    }
});
