<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Dashboard - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro Client</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="client-dashboard.html" class="nav-link active">Dashboard</a></li>
                    <li><a href="#" class="nav-link">Browse Freelancers</a></li>
                    <li><a href="#" class="nav-link">My Projects</a></li>
                    <li><a href="#" class="nav-link">Messages</a></li>
                    <li><a href="#" class="nav-link">Payments</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="Client" class="user-avatar">
                    <span class="user-name" id="clientName">Client</span>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">Profile</a>
                        <a href="#" class="dropdown-item">Settings</a>
                        <a href="#" class="dropdown-item logout-btn">Logout</a>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Client Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1>Welcome back, <span id="welcomeName">Client</span> 👋</h1>
                    <p>Manage your projects and find the perfect freelancers</p>
                </div>

            </div>

            <!-- Freelancer Search Section -->
            <div class="search-section">
                <div class="search-container">
                    <div class="search-header">
                        <h2><i class="fas fa-search"></i> Find Freelancers</h2>
                        <p>Search and browse talented freelancers for your projects</p>
                    </div>

                    <div class="search-input-group">
                        <i class="fas fa-search"></i>
                        <input type="text" id="freelancerSearch" placeholder="Search freelancers by name, skills, or expertise..." class="search-input">
                    </div>

                    <div class="search-filters">
                        <select id="skillFilter" class="filter-select">
                            <option value="">All Skills</option>
                            <option value="web-development">Web Development</option>
                            <option value="mobile-development">Mobile Development</option>
                            <option value="ui-ux-design">UI/UX Design</option>
                            <option value="graphic-design">Graphic Design</option>
                            <option value="digital-marketing">Digital Marketing</option>
                            <option value="content-writing">Content Writing</option>
                            <option value="data-analysis">Data Analysis</option>
                            <option value="photography">Photography</option>
                        </select>
                        <select id="experienceFilter" class="filter-select">
                            <option value="">All Experience</option>
                            <option value="entry">Entry Level (0-2 years)</option>
                            <option value="mid">Mid Level (2-5 years)</option>
                            <option value="senior">Senior Level (5+ years)</option>
                        </select>
                        <select id="availabilityFilter" class="filter-select">
                            <option value="">All Availability</option>
                            <option value="available">Available Now</option>
                            <option value="busy">Busy</option>
                        </select>
                        <button id="clearFilters" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Freelancers Grid -->
            <div class="freelancers-section">
                <div class="section-header">
                    <h2>Available Freelancers</h2>
                    <div class="view-options">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <div id="freelancersGrid" class="freelancers-grid">
                    <!-- Freelancer cards will be populated by JavaScript -->
                </div>

                <div id="noResults" class="no-results" style="display: none;">
                    <i class="fas fa-search"></i>
                    <h3>No freelancers found</h3>
                    <p>Try adjusting your search criteria or filters</p>
                </div>
            </div>



        </div>
    </main>




    <script src="scripts/auth.js"></script>
    <script src="scripts/client-dashboard.js"></script>
</body>
</html>
