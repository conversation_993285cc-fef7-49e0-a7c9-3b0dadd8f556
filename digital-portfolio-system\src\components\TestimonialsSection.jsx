import { Star, Quote } from 'lucide-react'

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Entrepreneur & Author',
      company: 'The 4-Hour Workweek',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'We\'ve used Fiverr for Shopify web development, graphic design, and backend web development. Working with <PERSON><PERSON> makes my job a little easier every day.',
      highlight: 'Makes my job easier every day'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Chief Commercial Officer',
      company: 'Naadam',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'We\'ve used Fiverr for Shopify web development, graphic design, and backend web development. Working with <PERSON><PERSON> makes my job a little easier every day.',
      highlight: 'Exceptional quality and speed'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'Co-Founder',
      company: 'Besome<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'It\'s extremely exciting that Fiverr has freelancers from all over the world — it broadens the talent pool. One of the best things about Fiverr is that while we\'re sleeping, someone\'s working.',
      highlight: 'Global talent pool'
    },
    {
      name: 'Kay Kim',
      role: 'Co-Founder',
      company: 'Rooted',
      avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'When you want to create a business bigger than yourself, you need a lot of help. That\'s what Fiverr does.',
      highlight: 'Perfect for scaling business'
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            What they're saying about Fiverr
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trusted by entrepreneurs and businesses worldwide
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 relative group"
            >
              {/* Quote Icon */}
              <div className="absolute top-6 right-6 opacity-10 group-hover:opacity-20 transition-opacity">
                <Quote className="w-12 h-12 text-green-500" />
              </div>

              {/* Rating */}
              <div className="flex items-center mb-6">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-gray-700 text-lg leading-relaxed mb-8 relative z-10">
                "{testimonial.text}"
              </blockquote>

              {/* Highlight */}
              <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6 rounded-r-lg">
                <p className="text-green-700 font-semibold text-sm">
                  💡 "{testimonial.highlight}"
                </p>
              </div>

              {/* Author */}
              <div className="flex items-center">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-14 h-14 rounded-full object-cover mr-4 ring-2 ring-gray-100"
                />
                <div>
                  <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                  <p className="text-gray-600 text-sm">{testimonial.role}</p>
                  <p className="text-green-600 text-sm font-medium">{testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Video Testimonial Section */}
        <div className="mt-20 bg-gradient-to-r from-green-500 to-blue-600 rounded-3xl p-12 text-center text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <h3 className="text-3xl font-bold mb-4">
              See how Fiverr transforms businesses
            </h3>
            <p className="text-xl mb-8 opacity-90">
              Watch real stories from entrepreneurs who scaled their business with Fiverr
            </p>
            <button className="px-8 py-4 bg-white text-green-600 font-bold rounded-lg hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
              Watch Success Stories
            </button>
          </div>
          
          {/* Decorative Elements */}
          <div className="absolute top-4 left-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-4 right-4 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        </div>
      </div>
    </section>
  )
}

export default TestimonialsSection
