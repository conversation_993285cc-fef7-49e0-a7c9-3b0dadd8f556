import { useState } from 'react'
import { User, Briefcase, Search, Star, MessageCircle, Settings } from 'lucide-react'
import { useAuth } from './contexts/AuthContext'
import './styles/animations.css'
import Header from './components/Header'
import Hero from './components/Hero'
import FreelancerProfiles from './components/FreelancerProfiles'
import SearchFilters from './components/SearchFilters'
import FiverrFooter from './components/FiverrFooter'
import AuthModal from './components/AuthModal'
import ProfileBuilder from './components/ProfileBuilder'
import AnalyticsDashboard from './components/AnalyticsDashboard'
import Calendar from './components/Calendar'
import SkillCertification from './components/SkillCertification'
import DashboardRouter from './components/DashboardRouter'
import PortfolioSearch from './components/search/PortfolioSearch'
import EnhancedProfileBuilder from './components/profile/EnhancedProfileBuilder'
import ServicesSection from './components/ServicesSection'
import TestimonialsSection from './components/TestimonialsSection'

function App() {
  const { user, isAuthenticated } = useAuth()
  const [currentView, setCurrentView] = useState('home')
  const [searchFilters, setSearchFilters] = useState({
    skills: '',
    experience: '',
    location: '',
    priceRange: ''
  })
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState('login')

  // If user is authenticated and trying to access dashboard, show dashboard
  if (isAuthenticated && (currentView === 'dashboard' || window.location.pathname === '/dashboard')) {
    return <DashboardRouter />
  }

  const handleViewChange = (view) => {
    setCurrentView(view)
  }

  const handleFilterChange = (filters) => {
    setSearchFilters(filters)
  }

  const handleAuthAction = (mode) => {
    setAuthMode(mode)
    setShowAuthModal(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-green-50 to-blue-50 relative overflow-hidden">
      {/* Fiverr-style Background Elements */}
      <div className="fixed inset-0 z-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-green-400/15 to-blue-500/15 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-purple-400/15 to-pink-500/15 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/3 left-1/3 w-64 h-64 bg-gradient-to-r from-yellow-400/10 to-orange-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-gradient-to-l from-cyan-400/10 to-teal-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative z-10">
        <Header
          currentView={currentView}
          onViewChange={handleViewChange}
          onAuthAction={handleAuthAction}
        />

      <main className="pt-20">
        {currentView === 'home' && (
          <>
            <Hero onViewChange={handleViewChange} />
            <ServicesSection />
            <TestimonialsSection />
            <section className="py-20 relative bg-gray-800/30">
              <div className="container">
                <div className="text-center mb-16">
                  <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                    Why Choose <span className="text-gradient">PortfolioHub</span>?
                  </h2>
                  <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                    Experience the next generation of freelancer-client connections with enhanced customization,
                    intelligent matching, and comprehensive analytics.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  <div className="glass-card text-center group hover:scale-105 transition-all duration-500">
                    <div className="card-body">
                      <div className="w-16 h-16 bg-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                        <User className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-accent-400 transition-colors">Enhanced Customization</h3>
                      <p className="text-gray-300 leading-relaxed">
                        Create fully personalized profiles with advanced customization tools, unique branding options,
                        and interactive elements that make you stand out.
                      </p>
                    </div>
                  </div>

                  <div className="glass-card text-center group hover:scale-105 transition-all duration-500">
                    <div className="card-body">
                      <div className="w-16 h-16 bg-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                        <Search className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-accent-400 transition-colors">Smart Matching</h3>
                      <p className="text-gray-300 leading-relaxed">
                        Intelligent algorithms analyze skills, experience, and project requirements to create
                        perfect matches between freelancers and clients.
                      </p>
                    </div>
                  </div>

                  <div className="glass-card text-center group hover:scale-105 transition-all duration-500">
                    <div className="card-body">
                      <div className="w-16 h-16 bg-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                        <Star className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-success-400 transition-colors">Professional Analytics</h3>
                      <p className="text-gray-300 leading-relaxed">
                        Comprehensive analytics dashboard with real-time insights, performance metrics,
                        and actionable recommendations to boost your success.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </>
        )}

        {currentView === 'browse' && (
          <PortfolioSearch />
        )}

        {currentView === 'profile' && (
          <EnhancedProfileBuilder />
        )}

        {currentView === 'analytics' && (
          <section className="py-12 min-h-screen">
            <div className="container">
              <div className="mb-8">
                <h1 className="text-4xl font-bold text-white mb-4">Analytics Dashboard</h1>
                <p className="text-gray-300 text-lg">Track your portfolio performance and insights</p>
              </div>
              <AnalyticsDashboard />
            </div>
          </section>
        )}

        {currentView === 'calendar' && (
          <section className="py-12 min-h-screen">
            <div className="container">
              <div className="mb-8">
                <h1 className="text-4xl font-bold text-white mb-4">Calendar & Scheduling</h1>
                <p className="text-gray-300 text-lg">Manage your professional activities and appointments</p>
              </div>
              <Calendar />
            </div>
          </section>
        )}

        {currentView === 'certifications' && (
          <section className="py-12 min-h-screen">
            <div className="container">
              <div className="mb-8">
                <h1 className="text-4xl font-bold text-white mb-4">Skill Certifications</h1>
                <p className="text-gray-300 text-lg">Validate your expertise with professional assessments</p>
              </div>
              <SkillCertification />
            </div>
          </section>
        )}
      </main>

      <FiverrFooter />

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
      />
    </div>
  )
}

export default App
