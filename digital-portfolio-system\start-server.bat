@echo off
echo Starting Digital Portfolio System...
cd /d "%~dp0"
echo Current directory: %CD%
echo.

REM Add Node.js to PATH temporarily
set "NODE_PATH=C:\Program Files\nodejs"
set "PATH=%NODE_PATH%;%PATH%"

echo Checking if Node.js is accessible...
node --version
if %ERRORLEVEL% NEQ 0 (
    echo Node.js not found in PATH
    echo Trying alternative location...
    set "NODE_PATH=C:\Program Files (x86)\nodejs"
    set "PATH=%NODE_PATH%;%PATH%"
    node --version
    if %ERRORLEVEL% NEQ 0 (
        echo Node.js not found! Please check installation.
        pause
        exit /b 1
    )
)

echo Node.js found! Starting development server...
echo.
npm run dev
echo.
echo Server stopped. Exit code: %ERRORLEVEL%
pause
