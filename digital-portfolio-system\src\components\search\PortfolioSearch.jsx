import { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import AdvancedSearch from './AdvancedSearch'
import SearchResults from './SearchResults'
import { 
  Bookmark, 
  MessageCircle, 
  Eye, 
  TrendingUp,
  Users,
  Search as SearchIcon
} from 'lucide-react'

const PortfolioSearch = () => {
  const { user, isAuthenticated } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({})
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(false)
  const [totalResults, setTotalResults] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [savedProfiles, setSavedProfiles] = useState([])
  const [recentSearches, setRecentSearches] = useState([])

  // Mock search statistics
  const searchStats = {
    totalProfiles: 15847,
    activeFreelancers: 8932,
    newThisWeek: 234,
    topSkills: ['React', 'Python', 'UI/UX Design', 'Digital Marketing', 'Node.js']
  }

  // Load saved data on component mount
  useEffect(() => {
    if (isAuthenticated) {
      // Load saved profiles and recent searches from localStorage or API
      const saved = JSON.parse(localStorage.getItem(`savedProfiles_${user?.id}`) || '[]')
      const recent = JSON.parse(localStorage.getItem(`recentSearches_${user?.id}`) || '[]')
      setSavedProfiles(saved)
      setRecentSearches(recent)
    }
  }, [isAuthenticated, user])

  // Mock search function - in real app, this would call an API
  const performSearch = async (query, searchFilters) => {
    setLoading(true)
    
    // Save search to recent searches
    if (query.trim() && isAuthenticated) {
      const newSearch = {
        query,
        filters: searchFilters,
        timestamp: new Date().toISOString()
      }
      const updatedRecent = [newSearch, ...recentSearches.filter(s => s.query !== query)].slice(0, 5)
      setRecentSearches(updatedRecent)
      localStorage.setItem(`recentSearches_${user?.id}`, JSON.stringify(updatedRecent))
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock search results based on query and filters
    const mockResults = generateMockResults(query, searchFilters)
    
    setResults(mockResults.results)
    setTotalResults(mockResults.total)
    setTotalPages(Math.ceil(mockResults.total / 12))
    setCurrentPage(1)
    setLoading(false)
  }

  const generateMockResults = (query, searchFilters) => {
    // This would be replaced with actual API call
    const baseResults = [
      {
        id: 1,
        name: 'Sarah Johnson',
        title: 'Full-Stack Developer',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        location: 'San Francisco, CA',
        hourlyRate: 85,
        rating: 4.9,
        reviewCount: 127,
        skills: ['React', 'Node.js', 'TypeScript', 'AWS'],
        description: 'Experienced full-stack developer with 6+ years building scalable web applications.',
        availability: 'Available',
        completedProjects: 89,
        responseTime: '2 hours',
        verified: true,
        lastActive: '2 hours ago'
      },
      {
        id: 2,
        name: 'Michael Chen',
        title: 'UI/UX Designer',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        location: 'New York, NY',
        hourlyRate: 75,
        rating: 4.8,
        reviewCount: 94,
        skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
        description: 'Creative UI/UX designer focused on creating intuitive user experiences.',
        availability: 'Available',
        completedProjects: 67,
        responseTime: '1 hour',
        verified: true,
        lastActive: '1 hour ago'
      },
      {
        id: 3,
        name: 'Emily Rodriguez',
        title: 'Digital Marketing Specialist',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        location: 'Austin, TX',
        hourlyRate: 60,
        rating: 4.7,
        reviewCount: 156,
        skills: ['SEO', 'Google Ads', 'Content Marketing', 'Analytics'],
        description: 'Results-driven digital marketer with proven track record.',
        availability: 'Busy',
        completedProjects: 134,
        responseTime: '4 hours',
        verified: true,
        lastActive: '30 minutes ago'
      }
    ]

    // Filter results based on search criteria
    let filteredResults = baseResults

    if (query) {
      filteredResults = filteredResults.filter(profile => 
        profile.name.toLowerCase().includes(query.toLowerCase()) ||
        profile.title.toLowerCase().includes(query.toLowerCase()) ||
        profile.skills.some(skill => skill.toLowerCase().includes(query.toLowerCase())) ||
        profile.description.toLowerCase().includes(query.toLowerCase())
      )
    }

    if (searchFilters.skills && searchFilters.skills.length > 0) {
      filteredResults = filteredResults.filter(profile =>
        searchFilters.skills.some(skill => 
          profile.skills.some(profileSkill => 
            profileSkill.toLowerCase().includes(skill.toLowerCase())
          )
        )
      )
    }

    return {
      results: filteredResults,
      total: filteredResults.length * 8 // Simulate more results
    }
  }

  const handleSearch = (query, searchFilters) => {
    setSearchQuery(query)
    setFilters(searchFilters)
    performSearch(query, searchFilters)
  }

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters)
    if (searchQuery || Object.values(newFilters).some(v => v && (Array.isArray(v) ? v.length > 0 : true))) {
      performSearch(searchQuery, newFilters)
    }
  }

  const handlePageChange = (page) => {
    setCurrentPage(page)
    // In real app, this would fetch new page of results
  }

  const handleContactUser = (profile) => {
    if (!isAuthenticated) {
      // Show login modal or redirect to login
      alert('Please log in to contact freelancers')
      return
    }
    
    // Open contact modal or navigate to messaging
    console.log('Contacting user:', profile.name)
    alert(`Contact feature would open for ${profile.name}`)
  }

  const handleSaveProfile = (profile) => {
    if (!isAuthenticated) {
      alert('Please log in to save profiles')
      return
    }

    const isAlreadySaved = savedProfiles.some(p => p.id === profile.id)
    let updatedSaved

    if (isAlreadySaved) {
      updatedSaved = savedProfiles.filter(p => p.id !== profile.id)
    } else {
      updatedSaved = [...savedProfiles, { ...profile, savedAt: new Date().toISOString() }]
    }

    setSavedProfiles(updatedSaved)
    localStorage.setItem(`savedProfiles_${user?.id}`, JSON.stringify(updatedSaved))
  }

  const handleViewProfile = (profile) => {
    // Navigate to full profile view
    console.log('Viewing profile:', profile.name)
    alert(`Profile view would open for ${profile.name}`)
  }

  const handleRecentSearchClick = (recentSearch) => {
    setSearchQuery(recentSearch.query)
    setFilters(recentSearch.filters)
    performSearch(recentSearch.query, recentSearch.filters)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Find the Perfect Portfolio
          </h1>
          <p className="text-gray-600 text-lg">
            Discover talented professionals and explore their work
          </p>
        </div>

        {/* Search Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-primary-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">
                  {searchStats.totalProfiles.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Total Portfolios</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">
                  {searchStats.activeFreelancers.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Active This Week</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <SearchIcon className="w-8 h-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">
                  {searchStats.newThisWeek}
                </p>
                <p className="text-sm text-gray-600">New This Week</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center">
              <Bookmark className="w-8 h-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-2xl font-bold text-gray-900">
                  {savedProfiles.length}
                </p>
                <p className="text-sm text-gray-600">Saved Profiles</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Searches */}
        {isAuthenticated && recentSearches.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Recent Searches</h3>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleRecentSearchClick(search)}
                  className="px-3 py-1 bg-white border border-gray-200 rounded-full text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  {search.query || 'Advanced Search'}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Search Component */}
        <div className="mb-8">
          <AdvancedSearch
            onSearch={handleSearch}
            onFilterChange={handleFilterChange}
            filters={filters}
          />
        </div>

        {/* Search Results */}
        <SearchResults
          results={results}
          loading={loading}
          totalResults={totalResults}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onContactUser={handleContactUser}
          onSaveProfile={handleSaveProfile}
          onViewProfile={handleViewProfile}
        />
      </div>
    </div>
  )
}

export default PortfolioSearch
