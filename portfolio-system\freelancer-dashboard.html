<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freelancer Dashboard - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="freelancer-dashboard.html" class="nav-link active">Dashboard</a></li>
                    <li><a href="#" class="nav-link">Messages</a></li>
                    <li><a href="#" class="nav-link">Projects</a></li>
                    <li><a href="#" class="nav-link">Analytics</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="User" class="user-avatar">
                    <span class="user-name">Eleni Berhan</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1>Welcome back, Eleni! 👋</h1>
                    <p>Manage your freelance business efficiently</p>
                </div>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="openModal('profileModal')">
                        <i class="fas fa-user-edit"></i>
                        Edit Profile
                    </button>
                    <button class="btn btn-outline" onclick="exportPortfolio()">
                        <i class="fas fa-download"></i>
                        Export Portfolio
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="stat-content">
                        <h3>12</h3>
                        <p>Active Projects</p>
                        <span class="stat-change positive">+2 this week</span>
                    </div>
                </div>

                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>$4,250</h3>
                        <p>Total Earnings</p>
                        <span class="stat-change positive">+15% this month</span>
                    </div>
                </div>

                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3>4.9</h3>
                        <p>Average Rating</p>
                        <span class="stat-change positive">+0.2 this month</span>
                    </div>
                </div>

                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="stat-content">
                        <h3>1,234</h3>
                        <p>Profile Views</p>
                        <span class="stat-change positive">+89 this week</span>
                    </div>
                </div>
            </div>

            <!-- Dashboard Tabs -->
            <div class="dashboard-tabs">
                <button class="tab-btn active" onclick="switchTab('overview')">
                    <i class="fas fa-chart-line"></i>
                    Overview
                </button>
                <button class="tab-btn" onclick="switchTab('profile')">
                    <i class="fas fa-user"></i>
                    Profile Management
                </button>
                <button class="tab-btn" onclick="switchTab('calendar')">
                    <i class="fas fa-calendar"></i>
                    Calendar & Schedule
                </button>
                <button class="tab-btn" onclick="switchTab('skills')">
                    <i class="fas fa-certificate"></i>
                    Skills & Certification
                </button>
                <button class="tab-btn" onclick="switchTab('search')">
                    <i class="fas fa-search"></i>
                    Find Work
                </button>
                <button class="tab-btn" onclick="switchTab('analytics')">
                    <i class="fas fa-chart-bar"></i>
                    Analytics
                </button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Overview Tab -->
                <div id="overview" class="tab-pane active">
                    <div class="dashboard-grid">
                        <!-- Recent Projects -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Projects</h3>
                                <a href="#" class="view-all">View All</a>
                            </div>
                            <div class="card-body">
                                <div class="project-list">
                                    <div class="project-item">
                                        <div class="project-info">
                                            <h4>E-commerce Website</h4>
                                            <p>Modern online store design</p>
                                            <div class="project-meta">
                                                <span class="project-status in-progress">In Progress</span>
                                                <span class="project-deadline">Due: Dec 15</span>
                                            </div>
                                        </div>
                                        <div class="project-value">$1,200</div>
                                    </div>

                                    <div class="project-item">
                                        <div class="project-info">
                                            <h4>Mobile App UI</h4>
                                            <p>iOS and Android app design</p>
                                            <div class="project-meta">
                                                <span class="project-status completed">Completed</span>
                                                <span class="project-deadline">Completed: Dec 10</span>
                                            </div>
                                        </div>
                                        <div class="project-value">$800</div>
                                    </div>

                                    <div class="project-item">
                                        <div class="project-info">
                                            <h4>Brand Identity</h4>
                                            <p>Logo and brand guidelines</p>
                                            <div class="project-meta">
                                                <span class="project-status pending">Pending</span>
                                                <span class="project-deadline">Due: Dec 20</span>
                                            </div>
                                        </div>
                                        <div class="project-value">$600</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Messages -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Messages</h3>
                                <a href="#" class="view-all">View All</a>
                            </div>
                            <div class="card-body">
                                <div class="message-list">
                                    <div class="message-item">
                                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="Client" class="message-avatar">
                                        <div class="message-content">
                                            <h4>Hawinet Mkeonen</h4>
                                            <p>Great work on the website! Can we schedule a call to discuss...</p>
                                            <span class="message-time">2 hours ago</span>
                                        </div>
                                        <div class="message-status unread"></div>
                                    </div>

                                    <div class="message-item">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Client" class="message-avatar">
                                        <div class="message-content">
                                            <h4>Mike Chen</h4>
                                            <p>The logo designs look amazing! I'd like to proceed with option 2.</p>
                                            <span class="message-time">5 hours ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Management Tab -->
                <div id="profile" class="tab-pane">
                    <div class="profile-management">
                        <!-- Profile Header -->
                        <div class="profile-header-section">
                            <div class="profile-cover-edit">
                                <div class="cover-image" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                                <button class="edit-cover-btn">
                                    <i class="fas fa-camera"></i>
                                    Edit Cover
                                </button>
                            </div>

                            <div class="profile-info-edit">
                                <div class="profile-avatar-section">
                                    <div class="profile-avatar">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face" alt="Eleni Berhan">
                                        <button class="edit-avatar-btn">
                                            <i class="fas fa-camera"></i>
                                        </button>
                                    </div>
                                    <div class="online-indicator"></div>
                                </div>

                                <div class="profile-details-edit">
                                    <div class="form-group">
                                        <label>Full Name</label>
                                        <input type="text" class="form-input" value="Eleni Berhan">
                                    </div>
                                    <div class="form-group">
                                        <label>Professional Title</label>
                                        <input type="text" class="form-input" value="Full Stack Developer & UI/UX Designer">
                                    </div>
                                    <div class="form-group">
                                        <label>Location</label>
                                        <input type="text" class="form-input" value="Addis Ababa, Ethiopia">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Content Management -->
                        <div class="profile-content-grid">
                            <!-- About Section -->
                            <div class="profile-edit-card">
                                <div class="card-header">
                                    <h3>About Me</h3>
                                    <button class="btn btn-outline btn-sm">
                                        <i class="fas fa-save"></i>
                                        Save
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>Bio</label>
                                        <textarea class="form-input" rows="4" placeholder="Tell clients about yourself...">Passionate full-stack developer with 5+ years of experience creating beautiful, functional web applications. I specialize in React, Node.js, and modern web technologies.</textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>Years of Experience</label>
                                        <input type="number" class="form-input" value="5">
                                    </div>
                                    <div class="form-group">
                                        <label>Hourly Rate ($)</label>
                                        <input type="number" class="form-input" value="75">
                                    </div>
                                </div>
                            </div>

                            <!-- Skills Management -->
                            <div class="profile-edit-card">
                                <div class="card-header">
                                    <h3>Skills</h3>
                                    <button class="btn btn-primary btn-sm" onclick="addSkill()">
                                        <i class="fas fa-plus"></i>
                                        Add Skill
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="skills-edit-list">
                                        <div class="skill-edit-item">
                                            <input type="text" class="form-input" value="JavaScript">
                                            <input type="range" class="skill-range" min="0" max="100" value="95">
                                            <span class="skill-percentage">95%</span>
                                            <button class="btn-remove">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>

                                        <div class="skill-edit-item">
                                            <input type="text" class="form-input" value="React">
                                            <input type="range" class="skill-range" min="0" max="100" value="90">
                                            <span class="skill-percentage">90%</span>
                                            <button class="btn-remove">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>

                                        <div class="skill-edit-item">
                                            <input type="text" class="form-input" value="Node.js">
                                            <input type="range" class="skill-range" min="0" max="100" value="85">
                                            <span class="skill-percentage">85%</span>
                                            <button class="btn-remove">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Portfolio Management -->
                            <div class="profile-edit-card full-width">
                                <div class="card-header">
                                    <h3>Portfolio Projects</h3>
                                    <button class="btn btn-primary btn-sm" onclick="addPortfolioItem()">
                                        <i class="fas fa-plus"></i>
                                        Add Project
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="portfolio-edit-grid">
                                        <div class="portfolio-edit-item">
                                            <div class="portfolio-image-upload">
                                                <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop" alt="Project">
                                                <div class="upload-overlay">
                                                    <button class="upload-btn">
                                                        <i class="fas fa-camera"></i>
                                                        Change Image
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="portfolio-edit-info">
                                                <input type="text" class="form-input" value="E-commerce Platform" placeholder="Project Title">
                                                <textarea class="form-input" rows="2" placeholder="Project Description">Modern online shopping experience with React and Node.js</textarea>
                                                <div class="portfolio-tags-edit">
                                                    <input type="text" class="form-input" placeholder="Add tags (comma separated)" value="React, Node.js, MongoDB">
                                                </div>
                                                <div class="portfolio-actions">
                                                    <button class="btn btn-outline btn-sm">
                                                        <i class="fas fa-save"></i>
                                                        Save
                                                    </button>
                                                    <button class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash"></i>
                                                        Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Media Links -->
                            <div class="profile-edit-card">
                                <div class="card-header">
                                    <h3>Social Media Links</h3>
                                </div>
                                <div class="card-body">
                                    <div class="social-links-edit">
                                        <div class="form-group">
                                            <label>
                                                <i class="fab fa-linkedin"></i>
                                                LinkedIn
                                            </label>
                                            <input type="url" class="form-input" placeholder="https://linkedin.com/in/eleni-berhan">
                                        </div>
                                        <div class="form-group">
                                            <label>
                                                <i class="fab fa-github"></i>
                                                GitHub
                                            </label>
                                            <input type="url" class="form-input" placeholder="https://github.com/eleni-berhan">
                                        </div>
                                        <div class="form-group">
                                            <label>
                                                <i class="fab fa-twitter"></i>
                                                Twitter
                                            </label>
                                            <input type="url" class="form-input" placeholder="https://twitter.com/eleni_berhan">
                                        </div>
                                        <div class="form-group">
                                            <label>
                                                <i class="fas fa-globe"></i>
                                                Website
                                            </label>
                                            <input type="url" class="form-input" placeholder="https://eleni-berhan.dev">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Privacy Settings -->
                            <div class="profile-edit-card">
                                <div class="card-header">
                                    <h3>Privacy & Security</h3>
                                </div>
                                <div class="card-body">
                                    <div class="privacy-settings">
                                        <div class="setting-item">
                                            <div class="setting-info">
                                                <h4>Portfolio Visibility</h4>
                                                <p>Control who can see your portfolio</p>
                                            </div>
                                            <select class="form-input">
                                                <option value="public">Public</option>
                                                <option value="private">Private</option>
                                                <option value="clients-only">Clients Only</option>
                                            </select>
                                        </div>

                                        <div class="setting-item">
                                            <div class="setting-info">
                                                <h4>Contact Information</h4>
                                                <p>Show contact details to potential clients</p>
                                            </div>
                                            <label class="toggle-switch">
                                                <input type="checkbox" checked>
                                                <span class="toggle-slider"></span>
                                            </label>
                                        </div>

                                        <div class="setting-item">
                                            <div class="setting-info">
                                                <h4>Analytics Tracking</h4>
                                                <p>Allow portfolio view tracking</p>
                                            </div>
                                            <label class="toggle-switch">
                                                <input type="checkbox" checked>
                                                <span class="toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar & Schedule Tab -->
                <div id="calendar" class="tab-pane">
                    <div class="calendar-section">
                        <div class="calendar-header">
                            <div class="calendar-controls">
                                <button class="btn btn-outline" onclick="previousMonth()">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <h3 id="currentMonth">December 2024</h3>
                                <button class="btn btn-outline" onclick="nextMonth()">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            <div class="calendar-actions">
                                <button class="btn btn-primary" onclick="openModal('eventModal')">
                                    <i class="fas fa-plus"></i>
                                    Add Event
                                </button>
                                <button class="btn btn-outline" onclick="toggleCalendarView()">
                                    <i class="fas fa-list"></i>
                                    List View
                                </button>
                            </div>
                        </div>

                        <div class="calendar-grid">
                            <div class="calendar-weekdays">
                                <div class="weekday">Sun</div>
                                <div class="weekday">Mon</div>
                                <div class="weekday">Tue</div>
                                <div class="weekday">Wed</div>
                                <div class="weekday">Thu</div>
                                <div class="weekday">Fri</div>
                                <div class="weekday">Sat</div>
                            </div>

                            <div class="calendar-days" id="calendarDays">
                                <!-- Calendar days will be generated by JavaScript -->
                            </div>
                        </div>

                        <div class="upcoming-events">
                            <h4>Upcoming Events</h4>
                            <div class="events-list">
                                <div class="event-item">
                                    <div class="event-time">
                                        <span class="event-date">Dec 15</span>
                                        <span class="event-hour">2:00 PM</span>
                                    </div>
                                    <div class="event-info">
                                        <h5>Client Meeting - Hawinet Mkeonen</h5>
                                        <p>Discuss website project requirements</p>
                                        <div class="event-type meeting">Meeting</div>
                                    </div>
                                    <div class="event-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="event-item">
                                    <div class="event-time">
                                        <span class="event-date">Dec 18</span>
                                        <span class="event-hour">10:00 AM</span>
                                    </div>
                                    <div class="event-info">
                                        <h5>Project Deadline - E-commerce Site</h5>
                                        <p>Final delivery and testing</p>
                                        <div class="event-type deadline">Deadline</div>
                                    </div>
                                    <div class="event-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="event-item">
                                    <div class="event-time">
                                        <span class="event-date">Dec 20</span>
                                        <span class="event-hour">3:30 PM</span>
                                    </div>
                                    <div class="event-info">
                                        <h5>Team Standup</h5>
                                        <p>Weekly progress review</p>
                                        <div class="event-type meeting">Meeting</div>
                                    </div>
                                    <div class="event-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Skills & Certification Tab -->
                <div id="skills" class="tab-pane">
                    <div class="skills-certification-section">
                        <!-- Skill Tests -->
                        <div class="skills-tests-card">
                            <div class="card-header">
                                <h3>Available Skill Tests</h3>
                                <p>Take tests to verify your skills and boost your credibility</p>
                            </div>
                            <div class="card-body">
                                <div class="skill-tests-grid">
                                    <div class="skill-test-item">
                                        <div class="test-icon">
                                            <i class="fab fa-js-square"></i>
                                        </div>
                                        <div class="test-info">
                                            <h4>JavaScript Fundamentals</h4>
                                            <p>Test your JavaScript knowledge</p>
                                            <div class="test-meta">
                                                <span class="test-duration">45 minutes</span>
                                                <span class="test-difficulty easy">Beginner</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary btn-sm">
                                            Take Test
                                        </button>
                                    </div>

                                    <div class="skill-test-item">
                                        <div class="test-icon">
                                            <i class="fab fa-react"></i>
                                        </div>
                                        <div class="test-info">
                                            <h4>React Development</h4>
                                            <p>Advanced React concepts and patterns</p>
                                            <div class="test-meta">
                                                <span class="test-duration">60 minutes</span>
                                                <span class="test-difficulty intermediate">Intermediate</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary btn-sm">
                                            Take Test
                                        </button>
                                    </div>

                                    <div class="skill-test-item">
                                        <div class="test-icon">
                                            <i class="fas fa-paint-brush"></i>
                                        </div>
                                        <div class="test-info">
                                            <h4>UI/UX Design Principles</h4>
                                            <p>Design fundamentals and best practices</p>
                                            <div class="test-meta">
                                                <span class="test-duration">50 minutes</span>
                                                <span class="test-difficulty intermediate">Intermediate</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary btn-sm">
                                            Take Test
                                        </button>
                                    </div>

                                    <div class="skill-test-item completed">
                                        <div class="test-icon">
                                            <i class="fab fa-node-js"></i>
                                        </div>
                                        <div class="test-info">
                                            <h4>Node.js Backend Development</h4>
                                            <p>Server-side JavaScript development</p>
                                            <div class="test-meta">
                                                <span class="test-duration">55 minutes</span>
                                                <span class="test-difficulty advanced">Advanced</span>
                                            </div>
                                            <div class="test-score">
                                                <i class="fas fa-trophy"></i>
                                                Score: 92%
                                            </div>
                                        </div>
                                        <button class="btn btn-success btn-sm" disabled>
                                            <i class="fas fa-check"></i>
                                            Completed
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Certifications -->
                        <div class="certifications-card">
                            <div class="card-header">
                                <h3>My Certifications</h3>
                                <button class="btn btn-primary btn-sm" onclick="addCertification()">
                                    <i class="fas fa-plus"></i>
                                    Add Certification
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="certifications-list">
                                    <div class="certification-item">
                                        <div class="cert-badge">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                        <div class="cert-info">
                                            <h4>AWS Certified Developer</h4>
                                            <p>Amazon Web Services</p>
                                            <div class="cert-meta">
                                                <span class="cert-date">Issued: Jan 2024</span>
                                                <span class="cert-expiry">Expires: Jan 2027</span>
                                            </div>
                                        </div>
                                        <div class="cert-actions">
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-eye"></i>
                                                View
                                            </button>
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-share"></i>
                                                Share
                                            </button>
                                        </div>
                                    </div>

                                    <div class="certification-item">
                                        <div class="cert-badge">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                        <div class="cert-info">
                                            <h4>Google UX Design Certificate</h4>
                                            <p>Google Career Certificates</p>
                                            <div class="cert-meta">
                                                <span class="cert-date">Issued: Mar 2023</span>
                                                <span class="cert-expiry">No Expiration</span>
                                            </div>
                                        </div>
                                        <div class="cert-actions">
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-eye"></i>
                                                View
                                            </button>
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-share"></i>
                                                Share
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Find Work Tab -->
                <div id="search" class="tab-pane">
                    <!-- Search Sub-tabs -->
                    <div class="search-sub-tabs">
                        <button class="sub-tab-btn active" onclick="switchSearchTab('projects')">
                            <i class="fas fa-project-diagram"></i>
                            Find Projects
                        </button>
                        <button class="sub-tab-btn" onclick="switchSearchTab('clients')">
                            <i class="fas fa-users"></i>
                            Find Clients
                        </button>
                    </div>

                    <!-- Projects Search -->
                    <div id="projects-search" class="search-sub-pane active">
                        <div class="project-search-section">
                            <!-- Project Search Header -->
                            <div class="search-header">
                                <h3>Find Projects & Opportunities</h3>
                                <p>Discover projects that match your skills and interests</p>
                            </div>

                        <!-- Advanced Project Search -->
                        <div class="project-search-filters">
                            <div class="search-filters-advanced">
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Search projects</label>
                                        <input type="text" class="form-input" placeholder="e.g., React development, Logo design">
                                    </div>
                                    <div class="filter-group">
                                        <label>Category</label>
                                        <select class="form-input">
                                            <option value="">All Categories</option>
                                            <option value="web-dev">Web Development</option>
                                            <option value="mobile-dev">Mobile Development</option>
                                            <option value="design">Design</option>
                                            <option value="marketing">Digital Marketing</option>
                                            <option value="writing">Writing & Translation</option>
                                            <option value="video">Video & Animation</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Budget Range</label>
                                        <select class="form-input">
                                            <option value="">Any Budget</option>
                                            <option value="under-500">Under $500</option>
                                            <option value="500-1000">$500 - $1,000</option>
                                            <option value="1000-2500">$1,000 - $2,500</option>
                                            <option value="2500-5000">$2,500 - $5,000</option>
                                            <option value="over-5000">Over $5,000</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>Project Duration</label>
                                        <select class="form-input">
                                            <option value="">Any Duration</option>
                                            <option value="short">Less than 1 month</option>
                                            <option value="medium">1-3 months</option>
                                            <option value="long">3+ months</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Experience Level</label>
                                        <select class="form-input">
                                            <option value="">Any Level</option>
                                            <option value="entry">Entry Level</option>
                                            <option value="intermediate">Intermediate</option>
                                            <option value="expert">Expert</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>Posted</label>
                                        <select class="form-input">
                                            <option value="">Any Time</option>
                                            <option value="today">Today</option>
                                            <option value="week">This Week</option>
                                            <option value="month">This Month</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="search-actions">
                                    <button class="btn btn-outline">Clear Filters</button>
                                    <button class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                        Search Projects
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Search Results -->
                        <div class="project-search-results">
                            <div class="results-header">
                                <h4>Available Projects (18 found)</h4>
                                <div class="sort-options">
                                    <select class="form-input">
                                        <option>Sort by Relevance</option>
                                        <option>Newest First</option>
                                        <option>Highest Budget</option>
                                        <option>Shortest Deadline</option>
                                    </select>
                                </div>
                            </div>

                            <div class="project-results-grid">
                                <div class="project-result-card">
                                    <div class="project-header">
                                        <h4>E-commerce Website Development</h4>
                                        <div class="project-budget">$2,500 - $4,000</div>
                                    </div>

                                    <div class="project-description">
                                        <p>Looking for an experienced developer to build a modern e-commerce website with React and Node.js. The project includes user authentication, payment integration, and admin dashboard.</p>
                                    </div>

                                    <div class="project-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <span>TechStart Inc.</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>Deadline: Jan 15, 2025</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>Posted: 2 hours ago</span>
                                        </div>
                                    </div>

                                    <div class="project-skills">
                                        <span class="skill-tag">React</span>
                                        <span class="skill-tag">Node.js</span>
                                        <span class="skill-tag">MongoDB</span>
                                        <span class="skill-tag">Payment Integration</span>
                                    </div>

                                    <div class="project-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-eye"></i>
                                            View Details
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="applyToProject('ecommerce-website')">
                                            <i class="fas fa-paper-plane"></i>
                                            Apply Now
                                        </button>
                                    </div>
                                </div>

                                <div class="project-result-card">
                                    <div class="project-header">
                                        <h4>Mobile App UI/UX Design</h4>
                                        <div class="project-budget">$1,200 - $2,000</div>
                                    </div>

                                    <div class="project-description">
                                        <p>Need a talented UI/UX designer to create modern, user-friendly designs for our fitness tracking mobile app. Looking for someone with experience in health/fitness apps.</p>
                                    </div>

                                    <div class="project-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <span>FitLife Solutions</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>Deadline: Dec 30, 2024</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>Posted: 5 hours ago</span>
                                        </div>
                                    </div>

                                    <div class="project-skills">
                                        <span class="skill-tag">UI/UX Design</span>
                                        <span class="skill-tag">Figma</span>
                                        <span class="skill-tag">Mobile Design</span>
                                        <span class="skill-tag">Prototyping</span>
                                    </div>

                                    <div class="project-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-eye"></i>
                                            View Details
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="applyToProject('mobile-app-design')">
                                            <i class="fas fa-paper-plane"></i>
                                            Apply Now
                                        </button>
                                    </div>
                                </div>

                                <div class="project-result-card">
                                    <div class="project-header">
                                        <h4>Digital Marketing Campaign</h4>
                                        <div class="project-budget">$800 - $1,500</div>
                                    </div>

                                    <div class="project-description">
                                        <p>Looking for a digital marketing expert to create and manage a comprehensive marketing campaign for our new product launch. Includes social media, SEO, and paid advertising.</p>
                                    </div>

                                    <div class="project-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <span>InnovateCorp</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>Deadline: Jan 20, 2025</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>Posted: 1 day ago</span>
                                        </div>
                                    </div>

                                    <div class="project-skills">
                                        <span class="skill-tag">SEO</span>
                                        <span class="skill-tag">Social Media</span>
                                        <span class="skill-tag">Google Ads</span>
                                        <span class="skill-tag">Analytics</span>
                                    </div>

                                    <div class="project-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-eye"></i>
                                            View Details
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="applyToProject('marketing-campaign')">
                                            <i class="fas fa-paper-plane"></i>
                                            Apply Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Clients Search -->
                    <div id="clients-search" class="search-sub-pane">
                        <div class="client-search-section">
                            <!-- Client Search Header -->
                            <div class="search-header">
                                <h3>Find Potential Clients</h3>
                                <p>Connect with businesses and individuals looking for your services</p>
                            </div>

                            <!-- Client Search Filters -->
                            <div class="client-search-filters">
                                <div class="search-filters-advanced">
                                    <div class="filter-row">
                                        <div class="filter-group">
                                            <label>Search clients</label>
                                            <input type="text" class="form-input" placeholder="e.g., Tech startups, Marketing agencies">
                                        </div>
                                        <div class="filter-group">
                                            <label>Industry</label>
                                            <select class="form-input">
                                                <option value="">All Industries</option>
                                                <option value="technology">Technology</option>
                                                <option value="healthcare">Healthcare</option>
                                                <option value="finance">Finance</option>
                                                <option value="education">Education</option>
                                                <option value="retail">Retail</option>
                                                <option value="marketing">Marketing</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="filter-row">
                                        <div class="filter-group">
                                            <label>Company Size</label>
                                            <select class="form-input">
                                                <option value="">Any Size</option>
                                                <option value="startup">Startup (1-10)</option>
                                                <option value="small">Small (11-50)</option>
                                                <option value="medium">Medium (51-200)</option>
                                                <option value="large">Large (200+)</option>
                                            </select>
                                        </div>
                                        <div class="filter-group">
                                            <label>Location</label>
                                            <select class="form-input">
                                                <option value="">Any Location</option>
                                                <option value="local">Local</option>
                                                <option value="remote">Remote Friendly</option>
                                                <option value="us">United States</option>
                                                <option value="europe">Europe</option>
                                                <option value="asia">Asia</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="search-actions">
                                        <button class="btn btn-outline">Clear Filters</button>
                                        <button class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                            Search Clients
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Client Search Results -->
                            <div class="client-search-results">
                                <div class="results-header">
                                    <h4>Potential Clients (12 found)</h4>
                                    <div class="sort-options">
                                        <select class="form-input">
                                            <option>Sort by Relevance</option>
                                            <option>Most Active</option>
                                            <option>Highest Budget</option>
                                            <option>Recently Joined</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="client-results-grid">
                                    <div class="client-result-card">
                                        <div class="client-header">
                                            <div class="client-info">
                                                <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=60&h=60&fit=crop" alt="Company" class="client-logo">
                                                <div class="client-details">
                                                    <h4>TechStart Inc.</h4>
                                                    <p>Technology Startup</p>
                                                    <div class="client-location">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        Addis Ababa, Ethiopia
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="client-status">
                                                <span class="status-badge active">Active</span>
                                            </div>
                                        </div>

                                        <div class="client-description">
                                            <p>Fast-growing tech startup specializing in AI-powered solutions. Looking for talented developers and designers for multiple ongoing projects.</p>
                                        </div>

                                        <div class="client-stats">
                                            <div class="stat-item">
                                                <span class="stat-value">15</span>
                                                <span class="stat-label">Projects Posted</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-value">$45K</span>
                                                <span class="stat-label">Total Spent</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-value">4.8</span>
                                                <span class="stat-label">Rating</span>
                                            </div>
                                        </div>

                                        <div class="client-needs">
                                            <span class="need-tag">React Development</span>
                                            <span class="need-tag">UI/UX Design</span>
                                            <span class="need-tag">Mobile Apps</span>
                                        </div>

                                        <div class="client-actions">
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-eye"></i>
                                                View Profile
                                            </button>
                                            <button class="btn btn-primary btn-sm" onclick="contactClient('techstart-inc')">
                                                <i class="fas fa-message"></i>
                                                Contact
                                            </button>
                                        </div>
                                    </div>

                                    <div class="client-result-card">
                                        <div class="client-header">
                                            <div class="client-info">
                                                <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=60&h=60&fit=crop" alt="Company" class="client-logo">
                                                <div class="client-details">
                                                    <h4>Creative Agency Pro</h4>
                                                    <p>Marketing & Design Agency</p>
                                                    <div class="client-location">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        Addis Ababa, Ethiopia
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="client-status">
                                                <span class="status-badge active">Active</span>
                                            </div>
                                        </div>

                                        <div class="client-description">
                                            <p>Full-service creative agency working with Fortune 500 companies. Regularly outsources specialized development and design work.</p>
                                        </div>

                                        <div class="client-stats">
                                            <div class="stat-item">
                                                <span class="stat-value">28</span>
                                                <span class="stat-label">Projects Posted</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-value">$78K</span>
                                                <span class="stat-label">Total Spent</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-value">4.9</span>
                                                <span class="stat-label">Rating</span>
                                            </div>
                                        </div>

                                        <div class="client-needs">
                                            <span class="need-tag">Web Development</span>
                                            <span class="need-tag">Branding</span>
                                            <span class="need-tag">Animation</span>
                                        </div>

                                        <div class="client-actions">
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-eye"></i>
                                                View Profile
                                            </button>
                                            <button class="btn btn-primary btn-sm" onclick="contactClient('creative-agency-pro')">
                                                <i class="fas fa-message"></i>
                                                Contact
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-pane">
                    <div class="analytics-section">
                        <!-- Analytics Overview -->
                        <div class="analytics-overview">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Portfolio Analytics</h3>
                                    <div class="date-range-selector">
                                        <select class="form-input">
                                            <option>Last 7 days</option>
                                            <option>Last 30 days</option>
                                            <option>Last 90 days</option>
                                            <option>Last year</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="analytics-stats-grid">
                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>1,234</h4>
                                                <p>Profile Views</p>
                                                <span class="stat-change positive">+12.5%</span>
                                            </div>
                                        </div>

                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-mouse-pointer"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>89</h4>
                                                <p>Portfolio Clicks</p>
                                                <span class="stat-change positive">+8.3%</span>
                                            </div>
                                        </div>

                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-message"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>23</h4>
                                                <p>Contact Requests</p>
                                                <span class="stat-change positive">+15.2%</span>
                                            </div>
                                        </div>

                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>2:45</h4>
                                                <p>Avg. Time on Portfolio</p>
                                                <span class="stat-change positive">+0:30</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Visitor Analytics -->
                        <div class="visitor-analytics">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Visitor Insights</h3>
                                </div>
                                <div class="card-body">
                                    <div class="visitor-chart">
                                        <div class="chart-placeholder">
                                            <div class="chart-bars">
                                                <div class="chart-bar" style="height: 60%"></div>
                                                <div class="chart-bar" style="height: 80%"></div>
                                                <div class="chart-bar" style="height: 45%"></div>
                                                <div class="chart-bar" style="height: 90%"></div>
                                                <div class="chart-bar" style="height: 70%"></div>
                                                <div class="chart-bar" style="height: 85%"></div>
                                                <div class="chart-bar" style="height: 95%"></div>
                                            </div>
                                            <div class="chart-labels">
                                                <span>Mon</span>
                                                <span>Tue</span>
                                                <span>Wed</span>
                                                <span>Thu</span>
                                                <span>Fri</span>
                                                <span>Sat</span>
                                                <span>Sun</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Popular Projects -->
                        <div class="popular-projects">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Most Viewed Projects</h3>
                                </div>
                                <div class="card-body">
                                    <div class="popular-projects-list">
                                        <div class="popular-project-item">
                                            <div class="project-thumbnail">
                                                <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=60&h=60&fit=crop" alt="Project">
                                            </div>
                                            <div class="project-info">
                                                <h4>E-commerce Platform</h4>
                                                <p>Modern online shopping experience</p>
                                            </div>
                                            <div class="project-stats">
                                                <span class="view-count">456 views</span>
                                                <span class="click-rate">12% CTR</span>
                                            </div>
                                        </div>

                                        <div class="popular-project-item">
                                            <div class="project-thumbnail">
                                                <img src="https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=60&h=60&fit=crop" alt="Project">
                                            </div>
                                            <div class="project-info">
                                                <h4>Mobile Banking App</h4>
                                                <p>Secure and intuitive banking solution</p>
                                            </div>
                                            <div class="project-stats">
                                                <span class="view-count">324 views</span>
                                                <span class="click-rate">8% CTR</span>
                                            </div>
                                        </div>

                                        <div class="popular-project-item">
                                            <div class="project-thumbnail">
                                                <img src="https://images.unsplash.com/photo-**********-d09347e92766?w=60&h=60&fit=crop" alt="Project">
                                            </div>
                                            <div class="project-info">
                                                <h4>Brand Identity Design</h4>
                                                <p>Complete branding solution</p>
                                            </div>
                                            <div class="project-stats">
                                                <span class="view-count">289 views</span>
                                                <span class="click-rate">15% CTR</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals -->
    <!-- Profile Modal -->
    <div id="profileModal" class="modal">
        <div class="modal-content large">
            <span class="close" onclick="closeModal('profileModal')">&times;</span>
            <h2>Edit Profile</h2>
            <form class="profile-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>Full Name</label>
                        <input type="text" class="form-input" value="Eleni Berhan">
                    </div>
                    <div class="form-group">
                        <label>Professional Title</label>
                        <input type="text" class="form-input" value="Full Stack Developer">
                    </div>
                </div>
                <div class="form-group">
                    <label>Bio</label>
                    <textarea class="form-input" rows="4">Passionate developer with 5+ years of experience...</textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Hourly Rate ($)</label>
                        <input type="number" class="form-input" value="75">
                    </div>
                    <div class="form-group">
                        <label>Location</label>
                        <input type="text" class="form-input" value="Addis Ababa, Ethiopia">
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('profileModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Event Modal -->
    <div id="eventModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('eventModal')">&times;</span>
            <h2>Add New Event</h2>
            <form class="event-form">
                <div class="form-group">
                    <label>Event Title</label>
                    <input type="text" class="form-input" placeholder="Enter event title" required>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea class="form-input" rows="3" placeholder="Event description"></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Date</label>
                        <input type="date" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label>Time</label>
                        <input type="time" class="form-input" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Event Type</label>
                    <select class="form-input" required>
                        <option value="">Select type</option>
                        <option value="meeting">Meeting</option>
                        <option value="deadline">Deadline</option>
                        <option value="reminder">Reminder</option>
                        <option value="personal">Personal</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('eventModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Event</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Export Options Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('exportModal')">&times;</span>
            <h2>Export Portfolio</h2>
            <div class="export-options">
                <div class="export-option">
                    <div class="export-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="export-info">
                        <h4>PDF Portfolio</h4>
                        <p>Professional PDF version of your portfolio</p>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="exportToPDF()">
                        <i class="fas fa-download"></i>
                        Export PDF
                    </button>
                </div>

                <div class="export-option">
                    <div class="export-icon">
                        <i class="fas fa-file-archive"></i>
                    </div>
                    <div class="export-info">
                        <h4>ZIP Archive</h4>
                        <p>Complete portfolio with all assets</p>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="exportToZIP()">
                        <i class="fas fa-download"></i>
                        Export ZIP
                    </button>
                </div>

                <div class="export-option">
                    <div class="export-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="export-info">
                        <h4>Shareable Link</h4>
                        <p>Generate a link to share your portfolio</p>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="generateShareLink()">
                        <i class="fas fa-share"></i>
                        Generate Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Application Modal -->
    <div id="projectApplicationModal" class="modal">
        <div class="modal-content large">
            <span class="close" onclick="closeModal('projectApplicationModal')">&times;</span>
            <h2>Apply to Project</h2>
            <form class="project-application-form">
                <div class="project-info-display">
                    <h4 id="applicationProjectTitle">E-commerce Website Development</h4>
                    <p id="applicationProjectBudget">$2,500 - $4,000</p>
                </div>

                <div class="form-group">
                    <label>Cover Letter</label>
                    <textarea class="form-input" rows="5" placeholder="Explain why you're the perfect fit for this project..." required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Your Bid Amount ($)</label>
                        <input type="number" class="form-input" placeholder="Enter your bid" required>
                    </div>
                    <div class="form-group">
                        <label>Delivery Time</label>
                        <select class="form-input" required>
                            <option value="">Select delivery time</option>
                            <option value="1-week">1 Week</option>
                            <option value="2-weeks">2 Weeks</option>
                            <option value="1-month">1 Month</option>
                            <option value="2-months">2 Months</option>
                            <option value="3-months">3+ Months</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Relevant Experience</label>
                    <textarea class="form-input" rows="3" placeholder="Describe your relevant experience for this project..."></textarea>
                </div>

                <div class="form-group">
                    <label>Portfolio Samples</label>
                    <div class="portfolio-samples">
                        <div class="sample-item">
                            <input type="checkbox" id="sample1">
                            <label for="sample1">E-commerce Platform</label>
                        </div>
                        <div class="sample-item">
                            <input type="checkbox" id="sample2">
                            <label for="sample2">React Dashboard</label>
                        </div>
                        <div class="sample-item">
                            <input type="checkbox" id="sample3">
                            <label for="sample3">Node.js API</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Additional Files (Optional)</label>
                    <input type="file" class="form-input" multiple accept=".pdf,.doc,.docx,.zip">
                    <small>Upload relevant documents, proposals, or examples</small>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('projectApplicationModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Application</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Back to Home -->
    <div class="floating-home-btn">
        <a href="index.html" class="btn btn-primary">
            <i class="fas fa-home"></i>
            Home
        </a>
    </div>

    <script src="scripts/main.js"></script>
    <script src="scripts/freelancer-dashboard.js"></script>
</body>
</html>