import { ArrowR<PERSON>, Users, Briefcase, TrendingUp, Sparkles, Star, Zap, Search, User } from 'lucide-react'

const Hero = ({ onViewChange }) => {
  const stats = [
    { label: 'Active Freelancers', value: '10,000+', icon: Users, color: 'from-accent-500 to-accent-600' },
    { label: 'Projects Completed', value: '50,000+', icon: Briefcase, color: 'from-primary-600 to-primary-700' },
    { label: 'Success Rate', value: '98%', icon: TrendingUp, color: 'from-success-500 to-success-600' }
  ]

  const features = [
    { icon: Sparkles, text: 'AI-Powered Matching' },
    { icon: Star, text: 'Premium Quality' },
    { icon: Zap, text: 'Lightning Fast' }
  ]

  return (
    <section className="relative py-20 overflow-hidden bg-gradient-to-br from-white via-green-50 to-blue-50">
      {/* Fiverr-style Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-64 h-64 bg-green-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-400/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-left">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-100 rounded-full border border-green-200 mb-8">
              <Sparkles className="w-4 h-4 text-green-600" />
              <span className="text-sm font-semibold text-green-700">Find the perfect freelance services for your business</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-6xl font-bold mb-8 leading-tight text-gray-900">
              Find the right <span className="text-green-500">freelance</span>
              <br />
              service, right away
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-lg">
              Work with talented people at the most affordable prices to get the most
              important work done. Start your project right now.
            </p>

            {/* Search Bar - Fiverr Style */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12 max-w-2xl">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Try 'building mobile app'"
                  className="w-full px-6 py-4 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              </div>
              <button className="px-8 py-4 bg-green-500 hover:bg-green-600 text-white font-semibold rounded-lg transition-all duration-200 flex items-center gap-2">
                <Search className="w-5 h-5" />
                Search
              </button>
            </div>

            {/* Popular Services */}
            <div className="mb-12">
              <p className="text-gray-600 mb-4">Popular:</p>
              <div className="flex flex-wrap gap-3">
                {['Website Design', 'WordPress', 'Logo Design', 'AI Services', 'Video Editing'].map((service, index) => (
                  <button
                    key={index}
                    className="px-4 py-2 border border-gray-300 rounded-full text-gray-700 hover:border-green-500 hover:text-green-600 transition-all duration-200"
                  >
                    {service}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content - Hero Image */}
          <div className="relative">
            <div className="bg-gradient-to-br from-green-400 to-blue-500 rounded-2xl p-8 shadow-2xl">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                    <User className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Sarah Johnson</h3>
                    <p className="text-gray-600 text-sm">UI/UX Designer</p>
                  </div>
                  <div className="ml-auto flex items-center">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium text-gray-700 ml-1">4.9</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="h-3 bg-gray-200 rounded-full">
                    <div className="h-3 bg-green-500 rounded-full w-4/5"></div>
                  </div>
                  <div className="h-3 bg-gray-200 rounded-full">
                    <div className="h-3 bg-blue-500 rounded-full w-3/5"></div>
                  </div>
                  <div className="h-3 bg-gray-200 rounded-full">
                    <div className="h-3 bg-purple-500 rounded-full w-5/6"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trusted By Section */}
        <div className="mt-20 text-center">
          <p className="text-gray-500 mb-8">Trusted by:</p>
          <div className="flex justify-center items-center space-x-12 opacity-60">
            {['FACEBOOK', 'GOOGLE', 'NETFLIX', 'P&G', 'PAYPAL'].map((company, index) => (
              <div key={index} className="text-2xl font-bold text-gray-400">
                {company}
              </div>
            ))}
          </div>
        </div>


      </div>


    </section>
  )
}

export default Hero
