import { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import DashboardLayout from '../layout/DashboardLayout'
import {
  Users,
  BarChart3,
  Shield,
  Settings,
  FileText,
  AlertTriangle,
  TrendingUp,
  UserCheck,
  DollarSign,
  Activity,
  Eye,
  MessageSquare,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Ban,
  CheckCircle,
  XCircle,
  Flag,
  Mail,
  Phone,
  Calendar
} from 'lucide-react'

const AdminDashboard = () => {
  const { user } = useAuth()
  const [activeView, setActiveView] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [userFilter, setUserFilter] = useState('all')
  const [selectedUsers, setSelectedUsers] = useState([])
  const [showUserModal, setShowUserModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)

  // Mock admin data
  const adminStats = {
    totalUsers: 12847,
    activeUsers: 8934,
    totalProjects: 5632,
    revenue: 284750,
    newUsersToday: 47,
    activeProjects: 1234,
    pendingReports: 12,
    systemHealth: 98.5
  }

  // Mock user data for management
  const mockUsers = [
    {
      id: 1,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'freelancer',
      status: 'active',
      joinDate: '2024-01-15',
      lastActive: '2 hours ago',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      projects: 89,
      earnings: 45000,
      rating: 4.9,
      verified: true,
      phone: '+****************',
      location: 'San Francisco, CA'
    },
    {
      id: 2,
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'client',
      status: 'active',
      joinDate: '2024-02-20',
      lastActive: '1 day ago',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      projects: 12,
      spent: 25000,
      rating: 4.7,
      verified: true,
      phone: '+****************',
      location: 'New York, NY'
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      role: 'freelancer',
      status: 'suspended',
      joinDate: '2023-11-10',
      lastActive: '1 week ago',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      projects: 156,
      earnings: 78000,
      rating: 4.6,
      verified: false,
      phone: '+****************',
      location: 'Austin, TX'
    },
    {
      id: 4,
      name: 'David Wilson',
      email: '<EMAIL>',
      role: 'client',
      status: 'pending',
      joinDate: '2024-06-01',
      lastActive: '3 days ago',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      projects: 3,
      spent: 5000,
      rating: 4.2,
      verified: false,
      phone: '+****************',
      location: 'Seattle, WA'
    }
  ]

  // User management functions
  const handleUserAction = (action, userId) => {
    const user = mockUsers.find(u => u.id === userId)
    switch (action) {
      case 'activate':
        alert(`Activating user: ${user.name}`)
        break
      case 'suspend':
        alert(`Suspending user: ${user.name}`)
        break
      case 'delete':
        if (confirm(`Are you sure you want to delete ${user.name}?`)) {
          alert(`Deleting user: ${user.name}`)
        }
        break
      case 'verify':
        alert(`Verifying user: ${user.name}`)
        break
      case 'edit':
        setSelectedUser(user)
        setShowUserModal(true)
        break
      default:
        break
    }
  }

  const handleBulkAction = (action) => {
    if (selectedUsers.length === 0) {
      alert('Please select users first')
      return
    }

    switch (action) {
      case 'activate':
        alert(`Activating ${selectedUsers.length} users`)
        break
      case 'suspend':
        alert(`Suspending ${selectedUsers.length} users`)
        break
      case 'delete':
        if (confirm(`Are you sure you want to delete ${selectedUsers.length} users?`)) {
          alert(`Deleting ${selectedUsers.length} users`)
        }
        break
      default:
        break
    }
    setSelectedUsers([])
  }

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = userFilter === 'all' || user.role === userFilter || user.status === userFilter
    return matchesSearch && matchesFilter
  })

  // Mock content moderation data
  const reportedContent = [
    {
      id: 1,
      type: 'portfolio',
      title: 'Web Development Portfolio',
      author: 'John Smith',
      reportedBy: 'Sarah Johnson',
      reason: 'Inappropriate content',
      status: 'pending',
      reportDate: '2024-06-28',
      description: 'Portfolio contains inappropriate images and misleading information about skills.',
      severity: 'high',
      category: 'content_violation'
    },
    {
      id: 2,
      type: 'message',
      title: 'Project Discussion',
      author: 'Mike Chen',
      reportedBy: 'Emily Rodriguez',
      reason: 'Harassment',
      status: 'under_review',
      reportDate: '2024-06-27',
      description: 'User sent inappropriate messages during project negotiation.',
      severity: 'critical',
      category: 'harassment'
    },
    {
      id: 3,
      type: 'profile',
      title: 'Freelancer Profile',
      author: 'Alex Wilson',
      reportedBy: 'David Brown',
      reason: 'Fake credentials',
      status: 'resolved',
      reportDate: '2024-06-25',
      description: 'Profile contains fabricated work experience and fake certifications.',
      severity: 'medium',
      category: 'fraud'
    }
  ]

  const handleContentAction = (action, contentId) => {
    const content = reportedContent.find(c => c.id === contentId)
    switch (action) {
      case 'approve':
        alert(`Approving content: ${content.title}`)
        break
      case 'remove':
        alert(`Removing content: ${content.title}`)
        break
      case 'warn':
        alert(`Warning user for content: ${content.title}`)
        break
      case 'ban':
        alert(`Banning user for content: ${content.title}`)
        break
      default:
        break
    }
  }

  const recentActivity = [
    { id: 1, type: 'user_registration', user: 'John Doe', action: 'registered as freelancer', time: '2 minutes ago' },
    { id: 2, type: 'project_posted', user: 'Tech Corp', action: 'posted new project', time: '15 minutes ago' },
    { id: 3, type: 'payment_completed', user: 'Sarah Johnson', action: 'completed payment $500', time: '1 hour ago' },
    { id: 4, type: 'report_submitted', user: 'Mike Chen', action: 'reported inappropriate content', time: '2 hours ago' }
  ]

  const navigation = [
    { id: 'overview', name: 'Overview', icon: BarChart3, active: activeView === 'overview', onClick: () => setActiveView('overview') },
    { id: 'users', name: 'User Management', icon: Users, active: activeView === 'users', onClick: () => setActiveView('users'), badge: '12' },
    { id: 'content', name: 'Content Moderation', icon: Shield, active: activeView === 'content', onClick: () => setActiveView('content'), badge: '5' },
    { id: 'analytics', name: 'Analytics', icon: TrendingUp, active: activeView === 'analytics', onClick: () => setActiveView('analytics') },
    { id: 'reports', name: 'Reports', icon: FileText, active: activeView === 'reports', onClick: () => setActiveView('reports') },
    { id: 'settings', name: 'System Settings', icon: Settings, active: activeView === 'settings', onClick: () => setActiveView('settings') }
  ]

  const StatCard = ({ title, value, change, icon: Icon, color = 'primary' }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">{value.toLocaleString()}</p>
          {change && (
            <p className={`text-sm mt-2 flex items-center ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              {change > 0 ? '+' : ''}{change}% from last month
            </p>
          )}
        </div>
        <div className={`w-12 h-12 bg-${color}-100 rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  )

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard title="Total Users" value={adminStats.totalUsers} change={12.5} icon={Users} />
        <StatCard title="Active Users" value={adminStats.activeUsers} change={8.2} icon={UserCheck} color="green" />
        <StatCard title="Total Revenue" value={adminStats.revenue} change={15.7} icon={DollarSign} color="blue" />
        <StatCard title="Active Projects" value={adminStats.activeProjects} change={-2.1} icon={Activity} color="purple" />
      </div>

      {/* Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Health */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="2"
                  strokeDasharray={`${adminStats.systemHealth}, 100`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-gray-900">{adminStats.systemHealth}%</span>
              </div>
            </div>
          </div>
          <p className="text-center text-sm text-gray-600 mt-4">All systems operational</p>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">{activity.user}</span> {activity.action}
                  </p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Review Reports</p>
              <p className="text-sm text-gray-600">{adminStats.pendingReports} pending</p>
            </div>
          </button>
          <button className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Users className="w-5 h-5 text-blue-500 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Manage Users</p>
              <p className="text-sm text-gray-600">View all users</p>
            </div>
          </button>
          <button className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <BarChart3 className="w-5 h-5 text-green-500 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">View Analytics</p>
              <p className="text-sm text-gray-600">Platform insights</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  )

  const renderUserManagement = () => (
    <div className="space-y-6">
      {/* User Management Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">User Management</h3>
            <p className="text-gray-600 mt-1">Manage platform users, roles, and permissions</p>
          </div>
          <div className="flex gap-3">
            <button className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
              Add User
            </button>
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              Export Users
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search users by name or email..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={userFilter}
              onChange={(e) => setUserFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Users</option>
              <option value="freelancer">Freelancers</option>
              <option value="client">Clients</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
              <option value="pending">Pending</option>
            </select>
            <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedUsers.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {selectedUsers.length} user{selectedUsers.length > 1 ? 's' : ''} selected
              </span>
              <div className="flex gap-2">
                <button
                  onClick={() => handleBulkAction('activate')}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                >
                  Activate
                </button>
                <button
                  onClick={() => handleBulkAction('suspend')}
                  className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 transition-colors"
                >
                  Suspend
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Users Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4">
                  <input
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(filteredUsers.map(u => u.id))
                      } else {
                        setSelectedUsers([])
                      }
                    }}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">User</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Role</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Activity</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Performance</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers([...selectedUsers, user.id])
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id))
                        }
                      }}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center">
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-10 h-10 rounded-full object-cover mr-3"
                      />
                      <div>
                        <div className="flex items-center">
                          <span className="font-medium text-gray-900">{user.name}</span>
                          {user.verified && (
                            <CheckCircle className="w-4 h-4 text-green-500 ml-1" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <p className="text-xs text-gray-500">{user.location}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      user.role === 'freelancer'
                        ? 'bg-blue-100 text-blue-800'
                        : user.role === 'client'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-purple-100 text-purple-800'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      user.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : user.status === 'suspended'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="text-sm">
                      <p className="text-gray-900">Last: {user.lastActive}</p>
                      <p className="text-gray-600">Joined: {new Date(user.joinDate).toLocaleDateString()}</p>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="text-sm">
                      {user.role === 'freelancer' ? (
                        <>
                          <p className="text-gray-900">{user.projects} projects</p>
                          <p className="text-gray-600">${user.earnings?.toLocaleString()} earned</p>
                          <div className="flex items-center mt-1">
                            <span className="text-yellow-500 mr-1">★</span>
                            <span className="text-gray-600">{user.rating}</span>
                          </div>
                        </>
                      ) : (
                        <>
                          <p className="text-gray-900">{user.projects} projects</p>
                          <p className="text-gray-600">${user.spent?.toLocaleString()} spent</p>
                        </>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleUserAction('edit', user.id)}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        title="Edit User"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUserAction('verify', user.id)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Verify User"
                      >
                        <CheckCircle className="w-4 h-4" />
                      </button>
                      {user.status === 'active' ? (
                        <button
                          onClick={() => handleUserAction('suspend', user.id)}
                          className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                          title="Suspend User"
                        >
                          <Ban className="w-4 h-4" />
                        </button>
                      ) : (
                        <button
                          onClick={() => handleUserAction('activate', user.id)}
                          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                          title="Activate User"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleUserAction('delete', user.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete User"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <p className="text-sm text-gray-600">
            Showing {filteredUsers.length} of {mockUsers.length} users
          </p>
          <div className="flex gap-2">
            <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors">
              Previous
            </button>
            <button className="px-3 py-1 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors">
              1
            </button>
            <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors">
              2
            </button>
            <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  const renderContentModeration = () => (
    <div className="space-y-6">
      {/* Content Moderation Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Content Moderation</h3>
            <p className="text-gray-600 mt-1">Review and moderate reported content and user violations</p>
          </div>
          <div className="flex gap-3">
            <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              <Flag className="w-4 h-4 mr-2 inline" />
              View All Reports
            </button>
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              Export Reports
            </button>
          </div>
        </div>

        {/* Moderation Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-red-800">Pending Reports</p>
                <p className="text-2xl font-bold text-red-900">
                  {reportedContent.filter(c => c.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <Eye className="w-8 h-8 text-yellow-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-yellow-800">Under Review</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {reportedContent.filter(c => c.status === 'under_review').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-green-800">Resolved</p>
                <p className="text-2xl font-bold text-green-900">
                  {reportedContent.filter(c => c.status === 'resolved').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center">
              <Shield className="w-8 h-8 text-purple-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-purple-800">Total Reports</p>
                <p className="text-2xl font-bold text-purple-900">{reportedContent.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Reported Content Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Content</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Reporter</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Reason</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Severity</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {reportedContent.map((content) => (
                <tr key={content.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <div>
                      <p className="font-medium text-gray-900">{content.title}</p>
                      <p className="text-sm text-gray-600">by {content.author}</p>
                      <p className="text-xs text-gray-500 mt-1">{content.description}</p>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      content.type === 'portfolio'
                        ? 'bg-blue-100 text-blue-800'
                        : content.type === 'message'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-purple-100 text-purple-800'
                    }`}>
                      {content.type}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <p className="text-sm text-gray-900">{content.reportedBy}</p>
                  </td>
                  <td className="py-4 px-4">
                    <p className="text-sm text-gray-900">{content.reason}</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full mt-1 ${
                      content.category === 'harassment'
                        ? 'bg-red-100 text-red-800'
                        : content.category === 'fraud'
                        ? 'bg-orange-100 text-orange-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {content.category}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      content.severity === 'critical'
                        ? 'bg-red-100 text-red-800'
                        : content.severity === 'high'
                        ? 'bg-orange-100 text-orange-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {content.severity}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      content.status === 'pending'
                        ? 'bg-red-100 text-red-800'
                        : content.status === 'under_review'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {content.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <p className="text-sm text-gray-900">{new Date(content.reportDate).toLocaleDateString()}</p>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleContentAction('approve', content.id)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Approve Content"
                      >
                        <CheckCircle className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleContentAction('remove', content.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Remove Content"
                      >
                        <XCircle className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleContentAction('warn', content.id)}
                        className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                        title="Warn User"
                      >
                        <AlertTriangle className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleContentAction('ban', content.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Ban User"
                      >
                        <Ban className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeView) {
      case 'overview':
        return renderOverview()
      case 'users':
        return renderUserManagement()
      case 'content':
        return renderContentModeration()
      case 'analytics':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Advanced analytics interface...</p></div>
      case 'reports':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>Reports interface...</p></div>
      case 'settings':
        return <div className="bg-white rounded-lg border border-gray-200 p-6"><p>System settings interface...</p></div>
      default:
        return renderOverview()
    }
  }

  return (
    <DashboardLayout
      navigation={navigation}
      title="Admin Dashboard"
      subtitle="Platform management and oversight"
    >
      {renderContent()}
    </DashboardLayout>
  )
}

export default AdminDashboard
