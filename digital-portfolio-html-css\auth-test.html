<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test - PortfolioPro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn.danger {
            background: #ef4444;
        }
        .btn.danger:hover {
            background: #dc2626;
        }
        .info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .user-info {
            background: #f0fdf4;
            border: 1px solid #22c55e;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            color: #dc2626;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication System Test</h1>
        
        <div class="info">
            <h3>Default Admin Account</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> admin123</p>
        </div>

        <div id="currentUserInfo" class="user-info" style="display: none;">
            <h3>Current User</h3>
            <div id="userDetails"></div>
        </div>

        <div class="container">
            <h3>Quick Login</h3>
            <button class="btn" onclick="loginAsAdmin()">Login as Admin</button>
            <button class="btn" onclick="createTestClient()">Create Test Client</button>
            <button class="btn" onclick="createTestFreelancer()">Create Test Freelancer</button>
            <button class="btn danger" onclick="clearAllData()">Clear All Data</button>
        </div>

        <div class="container">
            <h3>Navigation Test</h3>
            <button class="btn" onclick="goToAdmin()">Go to Admin Dashboard</button>
            <button class="btn" onclick="goToClient()">Go to Client Dashboard</button>
            <button class="btn" onclick="goToFreelancer()">Go to Freelancer Dashboard</button>
        </div>

        <div class="container">
            <h3>System Information</h3>
            <button class="btn" onclick="showUsers()">Show All Users</button>
            <button class="btn" onclick="showSession()">Show Current Session</button>
            <button class="btn" onclick="testPermissions()">Test Permissions</button>
        </div>

        <div id="output" class="container">
            <h3>Output</h3>
            <pre id="outputContent">Click buttons above to test the system...</pre>
        </div>
    </div>

    <script src="scripts/auth.js"></script>
    <script>
        // Update UI on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentUserDisplay();
        });

        function updateCurrentUserDisplay() {
            const currentUser = authSystem.getCurrentUser();
            const userInfoDiv = document.getElementById('currentUserInfo');
            const userDetailsDiv = document.getElementById('userDetails');
            
            if (currentUser) {
                userInfoDiv.style.display = 'block';
                userDetailsDiv.innerHTML = `
                    <p><strong>Name:</strong> ${currentUser.fullName}</p>
                    <p><strong>Email:</strong> ${currentUser.email}</p>
                    <p><strong>Role:</strong> ${currentUser.role}</p>
                    <p><strong>Status:</strong> ${currentUser.status}</p>
                `;
            } else {
                userInfoDiv.style.display = 'none';
            }
        }

        function loginAsAdmin() {
            authSystem.login('<EMAIL>', 'admin123')
                .then(result => {
                    if (result.success) {
                        output('Admin login successful!');
                        updateCurrentUserDisplay();
                    } else {
                        output('Admin login failed: ' + result.message, 'error');
                    }
                });
        }

        function createTestClient() {
            const clientData = {
                email: '<EMAIL>',
                password: 'test123',
                fullName: 'Test Client',
                role: 'client'
            };
            
            authSystem.register(clientData)
                .then(result => {
                    if (result.success) {
                        output('Test client created and logged in!');
                        updateCurrentUserDisplay();
                    } else {
                        output('Client creation failed: ' + result.message, 'error');
                    }
                });
        }

        function createTestFreelancer() {
            const freelancerData = {
                email: '<EMAIL>',
                password: 'test123',
                fullName: 'Test Freelancer',
                role: 'freelancer'
            };
            
            authSystem.register(freelancerData)
                .then(result => {
                    if (result.success) {
                        output('Test freelancer created! (Status: pending - needs admin approval)');
                        updateCurrentUserDisplay();
                    } else {
                        output('Freelancer creation failed: ' + result.message, 'error');
                    }
                });
        }

        function clearAllData() {
            if (confirm('This will clear all users and sessions. Continue?')) {
                localStorage.removeItem('portfolioPro_users');
                localStorage.removeItem('portfolioPro_session');
                authSystem.currentUser = null;
                authSystem.createDefaultAdmin(); // Recreate default admin
                output('All data cleared. Default admin recreated.');
                updateCurrentUserDisplay();
            }
        }

        function goToAdmin() {
            window.location.href = 'admin-dashboard.html';
        }

        function goToClient() {
            window.location.href = 'client-dashboard.html';
        }

        function goToFreelancer() {
            window.location.href = 'dashboard.html';
        }

        function showUsers() {
            const users = authSystem.getUsersFromStorage();
            output('All Users:\n' + JSON.stringify(users, null, 2));
        }

        function showSession() {
            const session = localStorage.getItem('portfolioPro_session');
            output('Current Session:\n' + (session || 'No session found'));
        }

        function testPermissions() {
            const currentUser = authSystem.getCurrentUser();
            if (!currentUser) {
                output('No user logged in', 'error');
                return;
            }
            
            const tests = [
                { role: 'admin', result: authSystem.hasPermission('admin') },
                { role: 'freelancer', result: authSystem.hasPermission('freelancer') },
                { role: 'client', result: authSystem.hasPermission('client') }
            ];
            
            let result = `Permission tests for ${currentUser.role}:\n`;
            tests.forEach(test => {
                result += `${test.role}: ${test.result ? 'ALLOWED' : 'DENIED'}\n`;
            });
            
            output(result);
        }

        function output(message, type = 'info') {
            const outputDiv = document.getElementById('outputContent');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : '';
            
            outputDiv.innerHTML = `[${timestamp}] ${message}`;
            if (className) {
                outputDiv.className = className;
            } else {
                outputDiv.className = '';
            }
        }
    </script>
</body>
</html>
