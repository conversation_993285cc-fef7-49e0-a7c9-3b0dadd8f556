import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  // Mock user data for demonstration - aligned with system requirements
  const mockUsers = {
    admin: {
      id: 1,
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      permissions: ['manage_users', 'view_analytics', 'moderate_content', 'system_settings'],
      lastLogin: new Date().toISOString(),
      isVerified: true
    },
    freelancer: {
      id: 2,
      email: '<EMAIL>',
      firstName: 'Sarah',
      lastName: '<PERSON>',
      role: 'freelancer',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      title: 'Full-Stack Web Developer',
      skills: ['React', 'Node.js', 'TypeScript', 'MongoDB'],
      hourlyRate: 75,
      rating: 4.9,
      completedProjects: 89,
      profileCompletion: 95,
      isVerified: true,
      availability: 'available',
      responseTime: '2 hours',
      successRate: 98
    },
    client: {
      id: 3,
      email: '<EMAIL>',
      firstName: 'Michael',
      lastName: 'Chen',
      role: 'client',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      company: 'Tech Innovations Inc.',
      projectsPosted: 15,
      totalSpent: 25000,
      memberSince: '2023-01-15',
      isVerified: true,
      activeProjects: 3
    }
  }

  useEffect(() => {
    // Check for existing session with security considerations
    try {
      const savedUser = localStorage.getItem('portfoliohub_user')
      const sessionExpiry = localStorage.getItem('portfoliohub_session_expiry')

      if (savedUser && sessionExpiry) {
        const now = new Date().getTime()
        const expiry = parseInt(sessionExpiry)

        if (now < expiry) {
          setUser(JSON.parse(savedUser))
        } else {
          // Session expired - clear storage
          localStorage.removeItem('portfoliohub_user')
          localStorage.removeItem('portfoliohub_session_expiry')
        }
      }
    } catch (error) {
      console.error('Error loading user session:', error)
      localStorage.removeItem('portfoliohub_user')
      localStorage.removeItem('portfoliohub_session_expiry')
    }
    setLoading(false)
  }, [])

  const login = async (email, password, userType = 'freelancer') => {
    try {
      setLoading(true)
      
      // Mock authentication - in real app, this would be an API call
      let userData = null
      
      if (email === '<EMAIL>' && password === 'admin123') {
        userData = mockUsers.admin
      } else if (email === '<EMAIL>' && password === 'freelancer123') {
        userData = mockUsers.freelancer
      } else if (email === '<EMAIL>' && password === 'client123') {
        userData = mockUsers.client
      } else {
        // For demo purposes, create a user based on userType
        userData = {
          id: Date.now(),
          email,
          firstName: 'Demo',
          lastName: 'User',
          role: userType,
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
        }
      }

      if (userData) {
        // Set session expiry (24 hours) for security
        const expiryTime = new Date().getTime() + (24 * 60 * 60 * 1000)

        setUser(userData)
        localStorage.setItem('portfoliohub_user', JSON.stringify(userData))
        localStorage.setItem('portfoliohub_session_expiry', expiryTime.toString())

        return { success: true, user: userData }
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (error) {
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData) => {
    try {
      setLoading(true)
      
      // Mock registration - in real app, this would be an API call
      const newUser = {
        id: Date.now(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.userType,
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        createdAt: new Date().toISOString()
      }

      setUser(newUser)
      localStorage.setItem('portfoliohub_user', JSON.stringify(newUser))
      return { success: true, user: newUser }
    } catch (error) {
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem('portfoliohub_user')
    localStorage.removeItem('portfoliohub_session_expiry')
  }

  const updateUser = (updates) => {
    const updatedUser = { ...user, ...updates }
    setUser(updatedUser)
    localStorage.setItem('portfoliohub_user', JSON.stringify(updatedUser))
  }

  const hasPermission = (permission) => {
    return user?.permissions?.includes(permission) || user?.role === 'admin'
  }

  const getDashboardRoute = () => {
    if (!user) return '/'
    switch (user.role) {
      case 'admin': return '/admin'
      case 'freelancer': return '/freelancer'
      case 'client': return '/client'
      default: return '/'
    }
  }

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateUser,
    hasPermission,
    getDashboardRoute,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin',
    isFreelancer: user?.role === 'freelancer',
    isClient: user?.role === 'client'
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthContext
