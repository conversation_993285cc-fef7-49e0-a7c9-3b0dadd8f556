// Freelancer Analytics System
// Provides comprehensive analytics for freelancers including portfolio views, visitor statistics, and engagement metrics

class FreelancerAnalytics {
    constructor() {
        this.analyticsData = this.loadAnalyticsData();
        this.init();
    }

    init() {
        this.generateMockData();
        this.setupEventListeners();
    }

    loadAnalyticsData() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return {};

        const storageKey = `freelancer_analytics_${currentUser.id}`;
        const stored = localStorage.getItem(storageKey);
        
        if (stored) {
            return JSON.parse(stored);
        }

        // Initialize default analytics data
        return {
            portfolioViews: {
                total: 0,
                thisMonth: 0,
                thisWeek: 0,
                today: 0,
                daily: Array(30).fill(0),
                monthly: Array(12).fill(0)
            },
            profileViews: {
                total: 0,
                thisMonth: 0,
                thisWeek: 0,
                today: 0
            },
            projectViews: {},
            visitors: {
                unique: 0,
                returning: 0,
                countries: {},
                referrers: {},
                devices: { desktop: 0, mobile: 0, tablet: 0 }
            },
            engagement: {
                averageTimeOnPage: 0,
                bounceRate: 0,
                contactFormSubmissions: 0,
                downloadCount: 0,
                socialClicks: 0
            },
            performance: {
                loadTime: 0,
                searchRanking: 0,
                seoScore: 85
            }
        };
    }

    saveAnalyticsData() {
        const currentUser = authSystem.getCurrentUser();
        if (!currentUser) return;

        const storageKey = `freelancer_analytics_${currentUser.id}`;
        localStorage.setItem(storageKey, JSON.stringify(this.analyticsData));
    }

    generateMockData() {
        // Generate realistic mock data for demonstration
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentDay = now.getDate();

        // Portfolio views
        this.analyticsData.portfolioViews.total = Math.floor(Math.random() * 5000) + 1000;
        this.analyticsData.portfolioViews.thisMonth = Math.floor(Math.random() * 500) + 100;
        this.analyticsData.portfolioViews.thisWeek = Math.floor(Math.random() * 150) + 30;
        this.analyticsData.portfolioViews.today = Math.floor(Math.random() * 25) + 5;

        // Generate daily views for the last 30 days
        for (let i = 0; i < 30; i++) {
            this.analyticsData.portfolioViews.daily[i] = Math.floor(Math.random() * 50) + 10;
        }

        // Generate monthly views for the year
        for (let i = 0; i < 12; i++) {
            this.analyticsData.portfolioViews.monthly[i] = Math.floor(Math.random() * 800) + 200;
        }

        // Profile views
        this.analyticsData.profileViews.total = Math.floor(this.analyticsData.portfolioViews.total * 0.8);
        this.analyticsData.profileViews.thisMonth = Math.floor(this.analyticsData.portfolioViews.thisMonth * 0.8);
        this.analyticsData.profileViews.thisWeek = Math.floor(this.analyticsData.portfolioViews.thisWeek * 0.8);
        this.analyticsData.profileViews.today = Math.floor(this.analyticsData.portfolioViews.today * 0.8);

        // Visitors
        this.analyticsData.visitors.unique = Math.floor(this.analyticsData.portfolioViews.total * 0.6);
        this.analyticsData.visitors.returning = Math.floor(this.analyticsData.portfolioViews.total * 0.4);
        
        // Countries
        this.analyticsData.visitors.countries = {
            'United States': Math.floor(Math.random() * 200) + 100,
            'United Kingdom': Math.floor(Math.random() * 150) + 80,
            'Canada': Math.floor(Math.random() * 100) + 50,
            'Germany': Math.floor(Math.random() * 80) + 40,
            'Australia': Math.floor(Math.random() * 60) + 30,
            'Ethiopia': Math.floor(Math.random() * 120) + 60
        };

        // Referrers
        this.analyticsData.visitors.referrers = {
            'Direct': Math.floor(Math.random() * 300) + 200,
            'Google': Math.floor(Math.random() * 250) + 150,
            'LinkedIn': Math.floor(Math.random() * 100) + 50,
            'GitHub': Math.floor(Math.random() * 80) + 40,
            'Twitter': Math.floor(Math.random() * 60) + 30,
            'Other': Math.floor(Math.random() * 50) + 20
        };

        // Devices
        const totalVisitors = this.analyticsData.visitors.unique + this.analyticsData.visitors.returning;
        this.analyticsData.visitors.devices.desktop = Math.floor(totalVisitors * 0.6);
        this.analyticsData.visitors.devices.mobile = Math.floor(totalVisitors * 0.3);
        this.analyticsData.visitors.devices.tablet = Math.floor(totalVisitors * 0.1);

        // Engagement
        this.analyticsData.engagement.averageTimeOnPage = Math.floor(Math.random() * 180) + 120; // seconds
        this.analyticsData.engagement.bounceRate = Math.floor(Math.random() * 30) + 20; // percentage
        this.analyticsData.engagement.contactFormSubmissions = Math.floor(Math.random() * 50) + 10;
        this.analyticsData.engagement.downloadCount = Math.floor(Math.random() * 100) + 25;
        this.analyticsData.engagement.socialClicks = Math.floor(Math.random() * 80) + 20;

        // Performance
        this.analyticsData.performance.loadTime = (Math.random() * 2 + 1).toFixed(2); // seconds
        this.analyticsData.performance.searchRanking = Math.floor(Math.random() * 10) + 1;
        this.analyticsData.performance.seoScore = Math.floor(Math.random() * 20) + 80;

        this.saveAnalyticsData();
    }

    renderAnalyticsDashboard() {
        this.renderOverviewStats();
        this.renderViewsChart();
        this.renderVisitorStats();
        this.renderEngagementMetrics();
        this.renderPerformanceMetrics();
        this.renderTopCountries();
        this.renderReferrerSources();
        this.renderDeviceBreakdown();
    }

    renderOverviewStats() {
        const stats = [
            {
                id: 'totalViews',
                value: this.analyticsData.portfolioViews.total.toLocaleString(),
                label: 'Total Portfolio Views',
                change: '+12.5%',
                changeType: 'positive'
            },
            {
                id: 'monthlyViews',
                value: this.analyticsData.portfolioViews.thisMonth.toLocaleString(),
                label: 'This Month',
                change: '+8.3%',
                changeType: 'positive'
            },
            {
                id: 'uniqueVisitors',
                value: this.analyticsData.visitors.unique.toLocaleString(),
                label: 'Unique Visitors',
                change: '+15.2%',
                changeType: 'positive'
            },
            {
                id: 'engagementRate',
                value: `${(100 - this.analyticsData.engagement.bounceRate)}%`,
                label: 'Engagement Rate',
                change: '****%',
                changeType: 'positive'
            }
        ];

        stats.forEach(stat => {
            const element = document.getElementById(stat.id);
            if (element) {
                element.textContent = stat.value;
            }
        });
    }

    renderViewsChart() {
        const chartContainer = document.getElementById('viewsChart');
        if (!chartContainer) return;

        const dailyViews = this.analyticsData.portfolioViews.daily;
        const maxViews = Math.max(...dailyViews);

        const chartHTML = dailyViews.map((views, index) => {
            const height = (views / maxViews) * 100;
            const date = new Date();
            date.setDate(date.getDate() - (29 - index));
            
            return `
                <div class="chart-bar" style="height: ${height}%" title="${views} views on ${date.toLocaleDateString()}">
                    <div class="bar-value">${views}</div>
                </div>
            `;
        }).join('');

        chartContainer.innerHTML = `
            <div class="chart-container">
                <div class="chart-bars">
                    ${chartHTML}
                </div>
                <div class="chart-labels">
                    <span>30 days ago</span>
                    <span>Today</span>
                </div>
            </div>
        `;
    }

    renderVisitorStats() {
        const visitorStatsContainer = document.getElementById('visitorStats');
        if (!visitorStatsContainer) return;

        const totalVisitors = this.analyticsData.visitors.unique + this.analyticsData.visitors.returning;
        const uniquePercentage = ((this.analyticsData.visitors.unique / totalVisitors) * 100).toFixed(1);
        const returningPercentage = ((this.analyticsData.visitors.returning / totalVisitors) * 100).toFixed(1);

        visitorStatsContainer.innerHTML = `
            <div class="visitor-breakdown">
                <div class="visitor-stat">
                    <div class="visitor-label">Unique Visitors</div>
                    <div class="visitor-value">${this.analyticsData.visitors.unique.toLocaleString()}</div>
                    <div class="visitor-percentage">${uniquePercentage}%</div>
                </div>
                <div class="visitor-stat">
                    <div class="visitor-label">Returning Visitors</div>
                    <div class="visitor-value">${this.analyticsData.visitors.returning.toLocaleString()}</div>
                    <div class="visitor-percentage">${returningPercentage}%</div>
                </div>
            </div>
        `;
    }

    renderEngagementMetrics() {
        const engagementContainer = document.getElementById('engagementMetrics');
        if (!engagementContainer) return;

        const avgTimeMinutes = Math.floor(this.analyticsData.engagement.averageTimeOnPage / 60);
        const avgTimeSeconds = this.analyticsData.engagement.averageTimeOnPage % 60;

        engagementContainer.innerHTML = `
            <div class="engagement-grid">
                <div class="engagement-metric">
                    <i class="fas fa-clock"></i>
                    <div class="metric-value">${avgTimeMinutes}m ${avgTimeSeconds}s</div>
                    <div class="metric-label">Avg. Time on Page</div>
                </div>
                <div class="engagement-metric">
                    <i class="fas fa-percentage"></i>
                    <div class="metric-value">${this.analyticsData.engagement.bounceRate}%</div>
                    <div class="metric-label">Bounce Rate</div>
                </div>
                <div class="engagement-metric">
                    <i class="fas fa-envelope"></i>
                    <div class="metric-value">${this.analyticsData.engagement.contactFormSubmissions}</div>
                    <div class="metric-label">Contact Forms</div>
                </div>
                <div class="engagement-metric">
                    <i class="fas fa-download"></i>
                    <div class="metric-value">${this.analyticsData.engagement.downloadCount}</div>
                    <div class="metric-label">Downloads</div>
                </div>
            </div>
        `;
    }

    renderPerformanceMetrics() {
        const performanceContainer = document.getElementById('performanceMetrics');
        if (!performanceContainer) return;

        performanceContainer.innerHTML = `
            <div class="performance-grid">
                <div class="performance-metric">
                    <div class="metric-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">${this.analyticsData.performance.loadTime}s</div>
                        <div class="metric-label">Page Load Time</div>
                        <div class="metric-status good">Good</div>
                    </div>
                </div>
                <div class="performance-metric">
                    <div class="metric-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">#${this.analyticsData.performance.searchRanking}</div>
                        <div class="metric-label">Search Ranking</div>
                        <div class="metric-status excellent">Excellent</div>
                    </div>
                </div>
                <div class="performance-metric">
                    <div class="metric-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">${this.analyticsData.performance.seoScore}/100</div>
                        <div class="metric-label">SEO Score</div>
                        <div class="metric-status good">Good</div>
                    </div>
                </div>
            </div>
        `;
    }

    renderTopCountries() {
        const countriesContainer = document.getElementById('topCountries');
        if (!countriesContainer) return;

        const countries = Object.entries(this.analyticsData.visitors.countries)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        const totalCountryVisitors = countries.reduce((sum, [,count]) => sum + count, 0);

        countriesContainer.innerHTML = countries.map(([country, count]) => {
            const percentage = ((count / totalCountryVisitors) * 100).toFixed(1);
            return `
                <div class="country-item">
                    <div class="country-info">
                        <span class="country-name">${country}</span>
                        <span class="country-count">${count.toLocaleString()}</span>
                    </div>
                    <div class="country-bar">
                        <div class="country-fill" style="width: ${percentage}%"></div>
                    </div>
                    <span class="country-percentage">${percentage}%</span>
                </div>
            `;
        }).join('');
    }

    renderReferrerSources() {
        const referrersContainer = document.getElementById('referrerSources');
        if (!referrersContainer) return;

        const referrers = Object.entries(this.analyticsData.visitors.referrers)
            .sort(([,a], [,b]) => b - a);

        const totalReferrers = referrers.reduce((sum, [,count]) => sum + count, 0);

        referrersContainer.innerHTML = referrers.map(([source, count]) => {
            const percentage = ((count / totalReferrers) * 100).toFixed(1);
            const iconClass = this.getReferrerIcon(source);
            
            return `
                <div class="referrer-item">
                    <div class="referrer-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="referrer-info">
                        <div class="referrer-name">${source}</div>
                        <div class="referrer-count">${count.toLocaleString()} visits</div>
                    </div>
                    <div class="referrer-percentage">${percentage}%</div>
                </div>
            `;
        }).join('');
    }

    renderDeviceBreakdown() {
        const devicesContainer = document.getElementById('deviceBreakdown');
        if (!devicesContainer) return;

        const devices = this.analyticsData.visitors.devices;
        const total = devices.desktop + devices.mobile + devices.tablet;

        const deviceData = [
            { name: 'Desktop', count: devices.desktop, icon: 'fas fa-desktop', color: '#3b82f6' },
            { name: 'Mobile', count: devices.mobile, icon: 'fas fa-mobile-alt', color: '#10b981' },
            { name: 'Tablet', count: devices.tablet, icon: 'fas fa-tablet-alt', color: '#f59e0b' }
        ];

        devicesContainer.innerHTML = deviceData.map(device => {
            const percentage = ((device.count / total) * 100).toFixed(1);
            return `
                <div class="device-item">
                    <div class="device-icon" style="color: ${device.color}">
                        <i class="${device.icon}"></i>
                    </div>
                    <div class="device-info">
                        <div class="device-name">${device.name}</div>
                        <div class="device-count">${device.count.toLocaleString()}</div>
                    </div>
                    <div class="device-percentage">${percentage}%</div>
                </div>
            `;
        }).join('');
    }

    getReferrerIcon(source) {
        const icons = {
            'Direct': 'fas fa-link',
            'Google': 'fab fa-google',
            'LinkedIn': 'fab fa-linkedin',
            'GitHub': 'fab fa-github',
            'Twitter': 'fab fa-twitter',
            'Other': 'fas fa-globe'
        };
        return icons[source] || 'fas fa-globe';
    }

    setupEventListeners() {
        // Period selector for charts
        const periodSelectors = document.querySelectorAll('.period-selector');
        periodSelectors.forEach(selector => {
            selector.addEventListener('change', (e) => {
                this.updateChartPeriod(e.target.value);
            });
        });

        // Refresh analytics data
        const refreshBtn = document.getElementById('refreshAnalytics');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.generateMockData();
                this.renderAnalyticsDashboard();
            });
        }
    }

    updateChartPeriod(period) {
        // Update chart based on selected period
        console.log(`Updating analytics for period: ${period}`);
        this.renderViewsChart();
    }

    // Track a new page view (to be called when portfolio is viewed)
    trackPageView(page = 'portfolio') {
        const today = new Date().toDateString();
        
        // Increment counters
        this.analyticsData.portfolioViews.total++;
        this.analyticsData.portfolioViews.today++;
        
        // Update daily data
        const dayIndex = new Date().getDate() - 1;
        if (dayIndex >= 0 && dayIndex < 30) {
            this.analyticsData.portfolioViews.daily[dayIndex]++;
        }

        this.saveAnalyticsData();
    }

    // Export analytics data
    exportAnalytics(format = 'json') {
        const data = {
            exportDate: new Date().toISOString(),
            freelancer: authSystem.getCurrentUser()?.fullName || 'Unknown',
            analytics: this.analyticsData
        };

        if (format === 'json') {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `freelancer-analytics-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    }
}

// Initialize analytics when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('freelancer-dashboard')) {
        window.freelancerAnalytics = new FreelancerAnalytics();
    }
});
