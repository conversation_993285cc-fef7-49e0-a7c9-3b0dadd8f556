<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Dashboard - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="client-dashboard.html" class="nav-link active">Dashboard</a></li>
                    <li><a href="#" class="nav-link">Find Freelancers</a></li>
                    <li><a href="#" class="nav-link">My Projects</a></li>
                    <li><a href="#" class="nav-link">Messages</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="User" class="user-avatar">
                    <span class="user-name">Sarah Johnson</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1>Welcome back, Sarah! 👋</h1>
                    <p>Manage your projects and find the perfect freelancers</p>
                </div>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="openModal('projectModal')">
                        <i class="fas fa-plus"></i>
                        Post Project
                    </button>
                    <button class="btn btn-outline" onclick="searchFreelancers()">
                        <i class="fas fa-search"></i>
                        Find Freelancers
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stat-content">
                        <h3>8</h3>
                        <p>Active Projects</p>
                        <span class="stat-change positive">+3 this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>15</h3>
                        <p>Freelancers Hired</p>
                        <span class="stat-change positive">+5 this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>$12,450</h3>
                        <p>Total Spent</p>
                        <span class="stat-change positive">+$2,300 this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>23</h3>
                        <p>Completed Projects</p>
                        <span class="stat-change positive">+4 this month</span>
                    </div>
                </div>
            </div>

            <!-- Dashboard Tabs -->
            <div class="dashboard-tabs">
                <button class="tab-btn active" onclick="switchTab('overview')">
                    <i class="fas fa-chart-line"></i>
                    Overview
                </button>
                <button class="tab-btn" onclick="switchTab('projects')">
                    <i class="fas fa-project-diagram"></i>
                    My Projects
                </button>
                <button class="tab-btn" onclick="switchTab('freelancers')">
                    <i class="fas fa-users"></i>
                    Find Freelancers
                </button>
                <button class="tab-btn" onclick="switchTab('messages')">
                    <i class="fas fa-message"></i>
                    Messages
                </button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Overview Tab -->
                <div id="overview" class="tab-pane active">
                    <div class="dashboard-grid">
                        <!-- Recent Projects -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Projects</h3>
                                <a href="#" class="view-all">View All</a>
                            </div>
                            <div class="card-body">
                                <div class="project-list">
                                    <div class="project-item">
                                        <div class="project-info">
                                            <h4>E-commerce Website Redesign</h4>
                                            <p>Complete redesign of online store</p>
                                            <div class="project-meta">
                                                <span class="project-status in-progress">In Progress</span>
                                                <span class="freelancer-name">John Smith</span>
                                            </div>
                                        </div>
                                        <div class="project-value">$2,500</div>
                                    </div>
                                    
                                    <div class="project-item">
                                        <div class="project-info">
                                            <h4>Mobile App Development</h4>
                                            <p>iOS and Android app for delivery service</p>
                                            <div class="project-meta">
                                                <span class="project-status completed">Completed</span>
                                                <span class="freelancer-name">Mike Chen</span>
                                            </div>
                                        </div>
                                        <div class="project-value">$4,200</div>
                                    </div>
                                    
                                    <div class="project-item">
                                        <div class="project-info">
                                            <h4>Brand Identity Package</h4>
                                            <p>Logo, business cards, and brand guidelines</p>
                                            <div class="project-meta">
                                                <span class="project-status pending">Pending</span>
                                                <span class="freelancer-name">Sarah Wilson</span>
                                            </div>
                                        </div>
                                        <div class="project-value">$800</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Freelancer Recommendations -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recommended Freelancers</h3>
                                <a href="#" class="view-all">View All</a>
                            </div>
                            <div class="card-body">
                                <div class="freelancer-recommendations">
                                    <div class="freelancer-rec-item">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" alt="Freelancer" class="freelancer-avatar">
                                        <div class="freelancer-info">
                                            <h4>Alex Rodriguez</h4>
                                            <p>Full Stack Developer</p>
                                            <div class="freelancer-rating">
                                                <div class="stars">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                                <span>4.9 (45 reviews)</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary btn-sm" onclick="contactFreelancer('alex-rodriguez')">
                                            Contact
                                        </button>
                                    </div>
                                    
                                    <div class="freelancer-rec-item">
                                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" alt="Freelancer" class="freelancer-avatar">
                                        <div class="freelancer-info">
                                            <h4>Emma Thompson</h4>
                                            <p>UI/UX Designer</p>
                                            <div class="freelancer-rating">
                                                <div class="stars">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                                <span>4.8 (67 reviews)</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary btn-sm" onclick="contactFreelancer('emma-thompson')">
                                            Contact
                                        </button>
                                    </div>
                                    
                                    <div class="freelancer-rec-item">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" alt="Freelancer" class="freelancer-avatar">
                                        <div class="freelancer-info">
                                            <h4>David Park</h4>
                                            <p>Digital Marketer</p>
                                            <div class="freelancer-rating">
                                                <div class="stars">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                                <span>4.7 (89 reviews)</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary btn-sm" onclick="contactFreelancer('david-park')">
                                            Contact
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Projects Tab -->
                <div id="projects" class="tab-pane">
                    <div class="projects-section">
                        <!-- Project Filters -->
                        <div class="project-filters">
                            <div class="filter-group">
                                <select class="filter-select">
                                    <option value="">All Projects</option>
                                    <option value="active">Active</option>
                                    <option value="completed">Completed</option>
                                    <option value="pending">Pending</option>
                                </select>
                                <select class="filter-select">
                                    <option value="">All Categories</option>
                                    <option value="web-dev">Web Development</option>
                                    <option value="design">Design</option>
                                    <option value="marketing">Marketing</option>
                                </select>
                                <input type="text" class="search-input" placeholder="Search projects...">
                            </div>
                            <button class="btn btn-primary" onclick="openModal('projectModal')">
                                <i class="fas fa-plus"></i>
                                Post New Project
                            </button>
                        </div>

                        <!-- Projects Grid -->
                        <div class="projects-grid">
                            <div class="project-card">
                                <div class="project-header">
                                    <h3>E-commerce Website Redesign</h3>
                                    <span class="project-status in-progress">In Progress</span>
                                </div>
                                <div class="project-details">
                                    <p>Complete redesign of our online store with modern UI/UX and improved functionality.</p>
                                    <div class="project-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <span>John Smith</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>Due: Dec 20, 2024</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-dollar-sign"></i>
                                            <span>$2,500</span>
                                        </div>
                                    </div>
                                    <div class="project-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 65%"></div>
                                        </div>
                                        <span class="progress-text">65% Complete</span>
                                    </div>
                                </div>
                                <div class="project-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <i class="fas fa-message"></i>
                                        Message
                                    </button>
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                        View Details
                                    </button>
                                </div>
                            </div>
                            
                            <div class="project-card">
                                <div class="project-header">
                                    <h3>Mobile App Development</h3>
                                    <span class="project-status completed">Completed</span>
                                </div>
                                <div class="project-details">
                                    <p>iOS and Android app for our delivery service with real-time tracking.</p>
                                    <div class="project-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <span>Mike Chen</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>Completed: Dec 10, 2024</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-dollar-sign"></i>
                                            <span>$4,200</span>
                                        </div>
                                    </div>
                                    <div class="project-rating">
                                        <span>Your Rating:</span>
                                        <div class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="project-actions">
                                    <button class="btn btn-outline btn-sm" onclick="writeRecommendation('mike-chen')">
                                        <i class="fas fa-thumbs-up"></i>
                                        Recommend
                                    </button>
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                        View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Find Freelancers Tab -->
                <div id="freelancers" class="tab-pane">
                    <div class="freelancer-search-section">
                        <!-- Advanced Search -->
                        <div class="advanced-search">
                            <div class="search-header">
                                <h3>Find the Perfect Freelancer</h3>
                                <p>Use filters to find freelancers that match your project needs</p>
                            </div>
                            
                            <div class="search-filters-advanced">
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Search by name or skills</label>
                                        <input type="text" class="form-input" placeholder="e.g., React developer, Logo designer">
                                    </div>
                                    <div class="filter-group">
                                        <label>Category</label>
                                        <select class="form-input">
                                            <option value="">All Categories</option>
                                            <option value="web-dev">Web Development</option>
                                            <option value="design">Design</option>
                                            <option value="marketing">Marketing</option>
                                            <option value="writing">Writing</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Experience Level</label>
                                        <select class="form-input">
                                            <option value="">Any Level</option>
                                            <option value="entry">Entry Level</option>
                                            <option value="intermediate">Intermediate</option>
                                            <option value="expert">Expert</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>Rating</label>
                                        <select class="form-input">
                                            <option value="">Any Rating</option>
                                            <option value="5">5 Stars</option>
                                            <option value="4">4+ Stars</option>
                                            <option value="3">3+ Stars</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Hourly Rate Range</label>
                                        <div class="rate-range">
                                            <input type="number" class="form-input" placeholder="Min $" min="0">
                                            <span>to</span>
                                            <input type="number" class="form-input" placeholder="Max $" min="0">
                                        </div>
                                    </div>
                                    <div class="filter-group">
                                        <label>Availability</label>
                                        <select class="form-input">
                                            <option value="">Any Availability</option>
                                            <option value="available">Available Now</option>
                                            <option value="busy">Busy</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="search-actions">
                                    <button class="btn btn-outline">Clear Filters</button>
                                    <button class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                        Search Freelancers
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Search Results -->
                        <div class="search-results">
                            <div class="results-header">
                                <h4>Search Results (24 freelancers found)</h4>
                                <div class="sort-options">
                                    <select class="form-input">
                                        <option>Sort by Relevance</option>
                                        <option>Highest Rated</option>
                                        <option>Lowest Rate</option>
                                        <option>Most Reviews</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="freelancers-results-grid">
                                <div class="freelancer-result-card">
                                    <div class="freelancer-header">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" alt="Freelancer" class="freelancer-avatar">
                                        <div class="freelancer-info">
                                            <h4>John Smith</h4>
                                            <p>Full Stack Developer</p>
                                            <div class="freelancer-rating">
                                                <div class="stars">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                                <span>5.0 (127 reviews)</span>
                                            </div>
                                        </div>
                                        <div class="freelancer-rate">
                                            <span class="rate">$75/hr</span>
                                        </div>
                                    </div>
                                    
                                    <div class="freelancer-description">
                                        <p>Experienced developer specializing in React, Node.js, and modern web technologies. 5+ years of experience building scalable applications.</p>
                                    </div>
                                    
                                    <div class="freelancer-skills">
                                        <span class="skill-tag">React</span>
                                        <span class="skill-tag">Node.js</span>
                                        <span class="skill-tag">MongoDB</span>
                                        <span class="skill-tag">TypeScript</span>
                                    </div>
                                    
                                    <div class="freelancer-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-eye"></i>
                                            View Profile
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="contactFreelancer('john-smith')">
                                            <i class="fas fa-message"></i>
                                            Contact
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messages Tab -->
                <div id="messages" class="tab-pane">
                    <div class="messages-section">
                        <div class="messages-layout">
                            <!-- Conversations List -->
                            <div class="conversations-list">
                                <div class="conversations-header">
                                    <h3>Messages</h3>
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus"></i>
                                        New
                                    </button>
                                </div>
                                
                                <div class="conversations">
                                    <div class="conversation-item active">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" alt="John Smith" class="conversation-avatar">
                                        <div class="conversation-info">
                                            <h4>John Smith</h4>
                                            <p>Thanks for the feedback! I'll make those changes...</p>
                                            <span class="conversation-time">2 hours ago</span>
                                        </div>
                                        <div class="conversation-status">
                                            <div class="unread-count">2</div>
                                        </div>
                                    </div>
                                    
                                    <div class="conversation-item">
                                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" alt="Sarah Wilson" class="conversation-avatar">
                                        <div class="conversation-info">
                                            <h4>Sarah Wilson</h4>
                                            <p>The logo designs are ready for review</p>
                                            <span class="conversation-time">1 day ago</span>
                                        </div>
                                    </div>
                                    
                                    <div class="conversation-item">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" alt="Mike Chen" class="conversation-avatar">
                                        <div class="conversation-info">
                                            <h4>Mike Chen</h4>
                                            <p>Project completed! Please review and approve</p>
                                            <span class="conversation-time">2 days ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Chat Area -->
                            <div class="chat-area">
                                <div class="chat-header">
                                    <div class="chat-user-info">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="John Smith" class="chat-avatar">
                                        <div class="chat-user-details">
                                            <h4>John Smith</h4>
                                            <span class="user-status online">Online</span>
                                        </div>
                                    </div>
                                    <div class="chat-actions">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-video"></i>
                                        </button>
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-phone"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="chat-messages">
                                    <div class="message received">
                                        <div class="message-content">
                                            <p>Hi Sarah! I've completed the initial design for your e-commerce website. Would you like to review it?</p>
                                            <span class="message-time">10:30 AM</span>
                                        </div>
                                    </div>
                                    
                                    <div class="message sent">
                                        <div class="message-content">
                                            <p>That's great! I'd love to see it. Can you share the preview link?</p>
                                            <span class="message-time">10:35 AM</span>
                                        </div>
                                    </div>
                                    
                                    <div class="message received">
                                        <div class="message-content">
                                            <p>Sure! Here's the link: https://preview.example.com/your-site</p>
                                            <p>I've also attached some screenshots for your reference.</p>
                                            <span class="message-time">10:37 AM</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="chat-input">
                                    <div class="input-container">
                                        <button class="attachment-btn">
                                            <i class="fas fa-paperclip"></i>
                                        </button>
                                        <input type="text" class="message-input" placeholder="Type your message...">
                                        <button class="send-btn">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals -->
    <!-- Post Project Modal -->
    <div id="projectModal" class="modal">
        <div class="modal-content large">
            <span class="close" onclick="closeModal('projectModal')">&times;</span>
            <h2>Post New Project</h2>
            <form class="project-form">
                <div class="form-group">
                    <label>Project Title</label>
                    <input type="text" class="form-input" placeholder="Enter project title" required>
                </div>

                <div class="form-group">
                    <label>Project Description</label>
                    <textarea class="form-input" rows="5" placeholder="Describe your project requirements in detail..." required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Category</label>
                        <select class="form-input" required>
                            <option value="">Select Category</option>
                            <option value="web-dev">Web Development</option>
                            <option value="mobile-dev">Mobile Development</option>
                            <option value="design">Design</option>
                            <option value="marketing">Digital Marketing</option>
                            <option value="writing">Writing & Translation</option>
                            <option value="video">Video & Animation</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Budget Range</label>
                        <select class="form-input" required>
                            <option value="">Select Budget</option>
                            <option value="under-500">Under $500</option>
                            <option value="500-1000">$500 - $1,000</option>
                            <option value="1000-2500">$1,000 - $2,500</option>
                            <option value="2500-5000">$2,500 - $5,000</option>
                            <option value="over-5000">Over $5,000</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Project Deadline</label>
                        <input type="date" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label>Experience Level Required</label>
                        <select class="form-input" required>
                            <option value="">Select Level</option>
                            <option value="entry">Entry Level</option>
                            <option value="intermediate">Intermediate</option>
                            <option value="expert">Expert</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Required Skills (comma separated)</label>
                    <input type="text" class="form-input" placeholder="e.g., React, Node.js, MongoDB">
                </div>

                <div class="form-group">
                    <label>Additional Requirements</label>
                    <textarea class="form-input" rows="3" placeholder="Any additional requirements or preferences..."></textarea>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('projectModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Post Project</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Recommendation Modal -->
    <div id="recommendationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('recommendationModal')">&times;</span>
            <h2>Write Recommendation</h2>
            <form class="recommendation-form">
                <div class="freelancer-info-display">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="Freelancer" class="freelancer-avatar">
                    <div class="freelancer-details">
                        <h4 id="recommendationFreelancerName">Mike Chen</h4>
                        <p>Digital Marketer</p>
                    </div>
                </div>

                <div class="form-group">
                    <label>Overall Rating</label>
                    <div class="rating-input">
                        <div class="stars-input">
                            <i class="fas fa-star" data-rating="1"></i>
                            <i class="fas fa-star" data-rating="2"></i>
                            <i class="fas fa-star" data-rating="3"></i>
                            <i class="fas fa-star" data-rating="4"></i>
                            <i class="fas fa-star" data-rating="5"></i>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Recommendation Title</label>
                    <input type="text" class="form-input" placeholder="e.g., Excellent work on marketing campaign" required>
                </div>

                <div class="form-group">
                    <label>Your Recommendation</label>
                    <textarea class="form-input" rows="5" placeholder="Share your experience working with this freelancer..." required></textarea>
                </div>

                <div class="form-group">
                    <label>Would you hire this freelancer again?</label>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="rehire" value="yes" required>
                            <span>Yes, definitely</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="rehire" value="maybe" required>
                            <span>Maybe</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="rehire" value="no" required>
                            <span>No</span>
                        </label>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('recommendationModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Recommendation</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Contact Freelancer Modal -->
    <div id="contactModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('contactModal')">&times;</span>
            <h2>Contact Freelancer</h2>
            <form class="contact-form">
                <div class="freelancer-info-display">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face" alt="Freelancer" class="freelancer-avatar">
                    <div class="freelancer-details">
                        <h4 id="contactFreelancerName">John Smith</h4>
                        <p>Full Stack Developer</p>
                        <div class="freelancer-rate">$75/hr</div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Subject</label>
                    <input type="text" class="form-input" placeholder="Enter message subject" required>
                </div>

                <div class="form-group">
                    <label>Message</label>
                    <textarea class="form-input" rows="5" placeholder="Describe your project and requirements..." required></textarea>
                </div>

                <div class="form-group">
                    <label>Project Budget (Optional)</label>
                    <input type="text" class="form-input" placeholder="e.g., $1,000 - $2,500">
                </div>

                <div class="form-group">
                    <label>Project Timeline (Optional)</label>
                    <input type="text" class="form-input" placeholder="e.g., 2-3 weeks">
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('contactModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Back to Home -->
    <div class="floating-home-btn">
        <a href="index.html" class="btn btn-primary">
            <i class="fas fa-home"></i>
            Home
        </a>
    </div>

    <script src="scripts/main.js"></script>
    <script src="scripts/client-dashboard.js"></script>
</body>
</html>
