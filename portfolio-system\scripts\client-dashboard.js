// Client Dashboard JavaScript

// Client Data
const clientData = {
    profile: {
        name: 'Hawinet Mkeonen',
        company: 'TechStart Inc.',
        email: '<EMAIL>'
    },
    stats: {
        activeProjects: 8,
        freelancersHired: 15,
        totalSpent: 12450,
        completedProjects: 23
    },
    projects: [
        {
            id: 1,
            title: 'E-commerce Website Redesign',
            description: 'Complete redesign of online store',
            freelancer: '<PERSON><PERSON> Be<PERSON>han',
            status: 'in-progress',
            budget: 2500,
            deadline: '2024-12-20',
            progress: 65
        },
        {
            id: 2,
            title: 'Mobile App Development',
            description: 'iOS and Android app for delivery service',
            freelancer: 'Hawinet Mkeonen',
            status: 'completed',
            budget: 4200,
            deadline: '2024-12-10',
            progress: 100
        }
    ],
    messages: [
        {
            id: 1,
            sender: 'El<PERSON> Be<PERSON>han',
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
            message: 'Thanks for the feedback! I\'ll make those changes...',
            time: '2 hours ago',
            unread: true
        }
    ]
};

// Initialize Client Dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeClientDashboard();
    setupClientEventListeners();
    updateClientStats();
    loadClientProjects();
});

function initializeClientDashboard() {
    console.log('Client Dashboard initialized');
    
    // Animate dashboard elements
    const dashboardCards = document.querySelectorAll('.dashboard-card, .stat-card');
    dashboardCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, index * 100);
    });
}

function setupClientEventListeners() {
    // Project management
    setupProjectManagement();
    
    // Freelancer search
    setupFreelancerSearch();
    
    // Messaging system
    setupMessaging();
    
    // Rating and recommendation system
    setupRatingSystem();
}

function updateClientStats() {
    // Update stats with real data
    const stats = clientData.stats;
    console.log('Client stats updated:', stats);
}

function loadClientProjects() {
    // Load and display client projects
    console.log('Loading client projects:', clientData.projects);
}

// Project Management
function setupProjectManagement() {
    // Project filters
    const filterSelects = document.querySelectorAll('.project-filters select');
    filterSelects.forEach(select => {
        select.addEventListener('change', filterProjects);
    });
    
    // Project search
    const searchInput = document.querySelector('.project-filters .search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(searchProjects, 300));
    }
    
    // Project cards interactions
    document.querySelectorAll('.project-card').forEach(card => {
        card.addEventListener('click', (e) => {
            if (!e.target.closest('.project-actions')) {
                const projectTitle = card.querySelector('h3').textContent;
                viewProjectDetails(projectTitle);
            }
        });
    });
}

function filterProjects() {
    const statusFilter = document.querySelector('.project-filters select:nth-child(1)').value;
    const categoryFilter = document.querySelector('.project-filters select:nth-child(2)').value;
    
    console.log('Filtering projects:', { status: statusFilter, category: categoryFilter });
    showNotification('Projects filtered', 'info');
}

function searchProjects() {
    const searchTerm = document.querySelector('.project-filters .search-input').value;
    console.log('Searching projects:', searchTerm);
}

function viewProjectDetails(projectTitle) {
    console.log('Viewing project details:', projectTitle);
    showNotification(`Opening ${projectTitle} details...`, 'info');
}

// Freelancer Search
function setupFreelancerSearch() {
    // Advanced search form
    const searchForm = document.querySelector('.advanced-search');
    if (searchForm) {
        const searchBtn = searchForm.querySelector('.btn-primary');
        if (searchBtn) {
            searchBtn.addEventListener('click', performAdvancedSearch);
        }
        
        const clearBtn = searchForm.querySelector('.btn-outline');
        if (clearBtn) {
            clearBtn.addEventListener('click', clearSearchFilters);
        }
    }
    
    // Sort options
    const sortSelect = document.querySelector('.sort-options select');
    if (sortSelect) {
        sortSelect.addEventListener('change', sortFreelancers);
    }
    
    // Freelancer result cards
    document.querySelectorAll('.freelancer-result-card').forEach(card => {
        card.addEventListener('click', (e) => {
            if (!e.target.closest('.freelancer-actions')) {
                const freelancerName = card.querySelector('h4').textContent;
                viewFreelancerProfile(freelancerName);
            }
        });
    });
}

function performAdvancedSearch() {
    const searchData = {
        query: document.querySelector('.advanced-search input[type="text"]').value,
        category: document.querySelector('.advanced-search select:nth-child(1)').value,
        experience: document.querySelector('.advanced-search select:nth-child(2)').value,
        rating: document.querySelector('.advanced-search select:nth-child(3)').value,
        minRate: document.querySelector('.rate-range input:first-child').value,
        maxRate: document.querySelector('.rate-range input:last-child').value,
        availability: document.querySelector('.advanced-search select:last-child').value
    };
    
    console.log('Advanced search:', searchData);
    showNotification('Searching freelancers...', 'info');
    
    // Simulate search results
    setTimeout(() => {
        showNotification('Found 24 freelancers matching your criteria', 'success');
    }, 1500);
}

function clearSearchFilters() {
    document.querySelectorAll('.advanced-search input, .advanced-search select').forEach(input => {
        input.value = '';
    });
    showNotification('Search filters cleared', 'info');
}

function sortFreelancers() {
    const sortBy = document.querySelector('.sort-options select').value;
    console.log('Sorting freelancers by:', sortBy);
    showNotification(`Sorted by ${sortBy}`, 'info');
}

function viewFreelancerProfile(freelancerName) {
    console.log('Viewing freelancer profile:', freelancerName);
    showNotification(`Opening ${freelancerName}'s profile...`, 'info');
}

// Messaging System
function setupMessaging() {
    // Conversation list
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all conversations
            document.querySelectorAll('.conversation-item').forEach(conv => {
                conv.classList.remove('active');
            });
            
            // Add active class to clicked conversation
            this.classList.add('active');
            
            const userName = this.querySelector('h4').textContent;
            loadConversation(userName);
        });
    });
    
    // Message input
    const messageInput = document.querySelector('.message-input');
    const sendBtn = document.querySelector('.send-btn');
    
    if (messageInput && sendBtn) {
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
    
    // Attachment button
    const attachmentBtn = document.querySelector('.attachment-btn');
    if (attachmentBtn) {
        attachmentBtn.addEventListener('click', handleAttachment);
    }
}

function loadConversation(userName) {
    console.log('Loading conversation with:', userName);
    
    // Update chat header
    const chatUserName = document.querySelector('.chat-user-details h4');
    if (chatUserName) {
        chatUserName.textContent = userName;
    }
    
    showNotification(`Loaded conversation with ${userName}`, 'info');
}

function sendMessage() {
    const messageInput = document.querySelector('.message-input');
    const messageText = messageInput.value.trim();
    
    if (messageText) {
        // Add message to chat
        addMessageToChat(messageText, 'sent');
        messageInput.value = '';
        
        // Simulate response
        setTimeout(() => {
            addMessageToChat('Thank you for your message! I\'ll get back to you soon.', 'received');
        }, 1000);
    }
}

function addMessageToChat(text, type) {
    const chatMessages = document.querySelector('.chat-messages');
    if (chatMessages) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${text}</p>
                <span class="message-time">${timeString}</span>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

function handleAttachment() {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = 'image/*,application/pdf,.doc,.docx';
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            showNotification(`Attached: ${file.name}`, 'success');
        });
    };
    input.click();
}

// Rating and Recommendation System
function setupRatingSystem() {
    // Star rating inputs
    document.querySelectorAll('.stars-input .fa-star').forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.getAttribute('data-rating'));
            setStarRating(this.parentElement, rating);
        });
        
        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.getAttribute('data-rating'));
            highlightStars(this.parentElement, rating);
        });
    });
    
    document.querySelectorAll('.stars-input').forEach(container => {
        container.addEventListener('mouseleave', function() {
            const currentRating = this.getAttribute('data-current-rating') || 0;
            highlightStars(this, currentRating);
        });
    });
}

function setStarRating(container, rating) {
    container.setAttribute('data-current-rating', rating);
    highlightStars(container, rating);
    showNotification(`Rated ${rating} stars`, 'info');
}

function highlightStars(container, rating) {
    const stars = container.querySelectorAll('.fa-star');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.style.color = '#fbbf24';
        } else {
            star.style.color = '#94a3b8';
        }
    });
}

function writeRecommendation(freelancerId) {
    openModal('recommendationModal');
    
    // Update modal with freelancer info
    const freelancerName = document.getElementById('recommendationFreelancerName');
    if (freelancerName) {
        freelancerName.textContent = freelancerId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
}

// Contact Functions
function contactFreelancer(freelancerName) {
    openModal('contactModal');
    
    // Update modal with freelancer info
    const nameElement = document.getElementById('contactFreelancerName');
    if (nameElement) {
        nameElement.textContent = freelancerName;
    }
}

// Project Posting
function postProject() {
    openModal('projectModal');
}

// Form Submissions
document.addEventListener('submit', function(e) {
    if (e.target.classList.contains('project-form')) {
        e.preventDefault();
        handleProjectSubmission(e.target);
    } else if (e.target.classList.contains('recommendation-form')) {
        e.preventDefault();
        handleRecommendationSubmission(e.target);
    } else if (e.target.classList.contains('contact-form')) {
        e.preventDefault();
        handleContactSubmission(e.target);
    }
});

function handleProjectSubmission(form) {
    const formData = new FormData(form);
    const projectData = Object.fromEntries(formData);
    
    console.log('Project posted:', projectData);
    
    showNotification('Posting project...', 'info');
    
    setTimeout(() => {
        showNotification('Project posted successfully!', 'success');
        closeModal('projectModal');
        form.reset();
    }, 2000);
}

function handleRecommendationSubmission(form) {
    const formData = new FormData(form);
    const recommendationData = Object.fromEntries(formData);
    
    console.log('Recommendation submitted:', recommendationData);
    
    showNotification('Submitting recommendation...', 'info');
    
    setTimeout(() => {
        showNotification('Recommendation submitted successfully!', 'success');
        closeModal('recommendationModal');
        form.reset();
    }, 1500);
}

function handleContactSubmission(form) {
    const formData = new FormData(form);
    const contactData = Object.fromEntries(formData);
    
    console.log('Contact message sent:', contactData);
    
    showNotification('Sending message...', 'info');
    
    setTimeout(() => {
        showNotification('Message sent successfully!', 'success');
        closeModal('contactModal');
        form.reset();
    }, 1500);
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Notification function (if not already defined)
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Export functions for global use
window.writeRecommendation = writeRecommendation;
window.contactFreelancer = contactFreelancer;
window.postProject = postProject;
window.viewProjectDetails = viewProjectDetails;
window.viewFreelancerProfile = viewFreelancerProfile;

console.log('Client Dashboard JavaScript loaded successfully!');
