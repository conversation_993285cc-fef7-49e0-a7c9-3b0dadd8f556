// Authentication System for 3-User Role System
// Handles login, registration, session management, and role-based access control

class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.sessionKey = 'portfolioPro_session';
        this.init();
    }

    init() {
        // Create default admin user if none exists
        this.createDefaultAdmin();

        // Check for existing session on page load
        this.loadSession();
        this.setupEventListeners();
    }

    createDefaultAdmin() {
        const users = this.getUsersFromStorage();
        const adminExists = users.some(user => user.role === 'admin');

        if (!adminExists) {
            const defaultAdmin = {
                id: 'admin_default_001',
                email: '<EMAIL>',
                password: 'admin123', // In production, this should be hashed
                fullName: 'System Administrator',
                role: 'admin',
                status: 'active',
                createdAt: new Date().toISOString(),
                profileData: {
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
                    location: 'System',
                    bio: 'Platform Administrator',
                    joinDate: new Date().toISOString()
                }
            };

            users.push(defaultAdmin);
            localStorage.setItem('portfolioPro_users', JSON.stringify(users));

            console.log('Default admin created: <EMAIL> / admin123');
        }
    }

    // Session Management
    loadSession() {
        const sessionData = localStorage.getItem(this.sessionKey);
        if (sessionData) {
            try {
                this.currentUser = JSON.parse(sessionData);

                // Only redirect if we're on the index page or login page
                const currentPage = window.location.pathname.split('/').pop();
                if (currentPage === 'index.html' || currentPage === '' || currentPage === '/') {
                    this.redirectToDashboard();
                }
            } catch (error) {
                console.error('Invalid session data:', error);
                this.clearSession();
            }
        }
    }

    saveSession(userData) {
        localStorage.setItem(this.sessionKey, JSON.stringify(userData));
        this.currentUser = userData;
    }

    clearSession() {
        localStorage.removeItem(this.sessionKey);
        this.currentUser = null;
    }

    // User Registration
    async register(formData) {
        // Check if email already exists
        const existingUsers = this.getUsersFromStorage();
        const emailExists = existingUsers.some(user => user.email === formData.get('email'));

        if (emailExists) {
            return {
                success: false,
                message: 'An account with this email already exists.'
            };
        }

        // Validate password strength
        const password = formData.get('password');
        const passwordValidation = this.validatePassword(password);
        if (!passwordValidation.isValid) {
            return {
                success: false,
                message: passwordValidation.message
            };
        }

        const userData = {
            id: this.generateUserId(),
            fullName: formData.get('fullName'),
            email: formData.get('email'),
            password: this.hashPassword(password), // Hash the password
            role: formData.get('role'),
            status: formData.get('role') === 'freelancer' ? 'pending' : 'unverified',
            createdAt: new Date().toISOString(),
            profileData: this.getDefaultProfileData(formData.get('role')),
            security: {
                emailVerified: false,
                emailVerificationToken: this.generateToken(),
                passwordResetToken: null,
                passwordResetExpiry: null,
                lastLogin: null,
                loginAttempts: 0,
                lockedUntil: null,
                twoFactorEnabled: false,
                securityQuestions: []
            }
        };

        // Simulate API call
        return new Promise((resolve) => {
            setTimeout(() => {
                // Save user to localStorage (simulating database)
                this.saveUserToStorage(userData);

                // Send verification email (simulated)
                this.sendVerificationEmail(userData);

                resolve({
                    success: true,
                    user: userData,
                    message: userData.role === 'freelancer'
                        ? 'Registration successful! Please check your email to verify your account. Your freelancer account will be pending approval after verification.'
                        : 'Registration successful! Please check your email to verify your account.'
                });
            }, 1000);
        });
    }

    // User Login
    async login(email, password) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u => u.email === email);

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'Invalid email or password.'
                    });
                    return;
                }

                const user = users[userIndex];

                // Check if account is locked
                if (user.security && user.security.lockedUntil && new Date() < new Date(user.security.lockedUntil)) {
                    const lockTime = Math.ceil((new Date(user.security.lockedUntil) - new Date()) / (1000 * 60));
                    resolve({
                        success: false,
                        message: `Account is locked due to multiple failed login attempts. Try again in ${lockTime} minutes.`
                    });
                    return;
                }

                // Verify password
                const isPasswordValid = this.verifyPassword(password, user.password);

                if (!isPasswordValid) {
                    // Increment login attempts
                    if (!user.security) user.security = {};
                    user.security.loginAttempts = (user.security.loginAttempts || 0) + 1;

                    // Lock account after 5 failed attempts
                    if (user.security.loginAttempts >= 5) {
                        user.security.lockedUntil = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 minutes
                        users[userIndex] = user;
                        localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                        resolve({
                            success: false,
                            message: 'Account locked due to multiple failed login attempts. Try again in 30 minutes.'
                        });
                        return;
                    }

                    users[userIndex] = user;
                    localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                    resolve({
                        success: false,
                        message: `Invalid email or password. ${5 - user.security.loginAttempts} attempts remaining.`
                    });
                    return;
                }

                // Reset login attempts on successful login
                if (user.security) {
                    user.security.loginAttempts = 0;
                    user.security.lockedUntil = null;
                    user.security.lastLogin = new Date().toISOString();
                }

                // Check account status
                if (user.status === 'suspended') {
                    resolve({
                        success: false,
                        message: 'Your account has been suspended. Please contact support.'
                    });
                    return;
                }

                if (!user.security || !user.security.emailVerified) {
                    resolve({
                        success: false,
                        message: 'Please verify your email address before logging in. Check your inbox for the verification link.',
                        requiresVerification: true,
                        userId: user.id
                    });
                    return;
                }

                if (user.role === 'freelancer' && user.status === 'pending') {
                    resolve({
                        success: false,
                        message: 'Your freelancer account is pending approval.'
                    });
                    return;
                }

                // Update user data and save
                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                this.saveSession(user);
                resolve({
                    success: true,
                    user: user,
                    message: 'Login successful!'
                });
            }, 800);
        });
    }

    // Logout
    logout() {
        this.clearSession();
        window.location.href = 'index.html';
    }

    // Role-based Dashboard Redirect
    redirectToDashboard() {
        if (!this.currentUser) {
            console.log('No current user for redirect');
            return;
        }

        const currentPage = window.location.pathname.split('/').pop();
        let targetPage;

        console.log('=== REDIRECT DEBUG ===');
        console.log('User role:', this.currentUser.role);
        console.log('Current page:', currentPage);
        console.log('Full user object:', this.currentUser);

        switch (this.currentUser.role) {
            case 'admin':
                targetPage = 'admin-dashboard.html';
                console.log('Admin user - targeting admin dashboard');
                break;
            case 'freelancer':
                targetPage = 'freelancer-dashboard.html';
                console.log('Freelancer user - targeting freelancer dashboard');
                break;
            case 'client':
                targetPage = 'client-dashboard.html';
                console.log('Client user - targeting client dashboard');
                break;
            default:
                targetPage = 'index.html';
                console.log('Unknown role - targeting index');
        }

        console.log('Target page determined:', targetPage);

        // Only redirect if not already on the correct page
        const specialPages = ['index.html', 'auth-test.html', 'test-auth-flow.html'];
        if (currentPage !== targetPage && !specialPages.includes(currentPage)) {
            console.log('REDIRECTING from', currentPage, 'to', targetPage);
            window.location.href = targetPage;
        } else {
            console.log('NOT redirecting - already on correct page or special page');
        }
        console.log('=== END REDIRECT DEBUG ===');
    }

    // Access Control
    hasPermission(requiredRole) {
        if (!this.currentUser) return false;
        
        const roleHierarchy = {
            'admin': 3,
            'freelancer': 2,
            'client': 1
        };
        
        return roleHierarchy[this.currentUser.role] >= roleHierarchy[requiredRole];
    }

    requireAuth(requiredRole = null) {
        console.log('=== REQUIRE AUTH DEBUG ===');
        console.log('RequireAuth called with role:', requiredRole);
        console.log('Current user:', this.currentUser);
        console.log('Current page:', window.location.pathname.split('/').pop());

        if (!this.currentUser) {
            console.log('No current user, redirecting to index');
            window.location.href = 'index.html';
            return false;
        }

        if (requiredRole && !this.hasPermission(requiredRole)) {
            console.log('Permission denied for role:', requiredRole, 'User role:', this.currentUser.role);
            console.log('Role hierarchy check failed - redirecting to appropriate dashboard');
            alert(`Access denied. You need ${requiredRole} permissions. Your role: ${this.currentUser.role}`);
            this.redirectToDashboard();
            return false;
        }

        console.log('Auth check passed for role:', requiredRole);
        console.log('=== END REQUIRE AUTH DEBUG ===');
        return true;
    }

    // Password Reset
    async requestPasswordReset(email) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u => u.email === email);

                if (userIndex === -1) {
                    // Don't reveal if email exists for security
                    resolve({
                        success: true,
                        message: 'If an account with this email exists, you will receive a password reset link.'
                    });
                    return;
                }

                const user = users[userIndex];
                const resetToken = this.generateToken();
                const resetExpiry = new Date(Date.now() + 60 * 60 * 1000).toISOString(); // 1 hour

                if (!user.security) user.security = {};
                user.security.passwordResetToken = resetToken;
                user.security.passwordResetExpiry = resetExpiry;

                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                // Simulate sending email
                this.sendPasswordResetEmail(user, resetToken);

                resolve({
                    success: true,
                    message: 'If an account with this email exists, you will receive a password reset link.'
                });
            }, 1000);
        });
    }

    async resetPassword(token, newPassword) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u =>
                    u.security &&
                    u.security.passwordResetToken === token &&
                    u.security.passwordResetExpiry &&
                    new Date() < new Date(u.security.passwordResetExpiry)
                );

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'Invalid or expired reset token.'
                    });
                    return;
                }

                // Validate new password
                const passwordValidation = this.validatePassword(newPassword);
                if (!passwordValidation.isValid) {
                    resolve({
                        success: false,
                        message: passwordValidation.message
                    });
                    return;
                }

                const user = users[userIndex];
                user.password = this.hashPassword(newPassword);
                user.security.passwordResetToken = null;
                user.security.passwordResetExpiry = null;
                user.security.loginAttempts = 0; // Reset login attempts
                user.security.lockedUntil = null; // Unlock account

                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                resolve({
                    success: true,
                    message: 'Password has been reset successfully. You can now log in with your new password.'
                });
            }, 800);
        });
    }

    // Email Verification
    async verifyEmail(token) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u =>
                    u.security && u.security.emailVerificationToken === token
                );

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'Invalid verification token.'
                    });
                    return;
                }

                const user = users[userIndex];
                user.security.emailVerified = true;
                user.security.emailVerificationToken = null;

                // Update status based on role
                if (user.role === 'freelancer') {
                    user.status = 'pending'; // Still needs admin approval
                } else {
                    user.status = 'active';
                }

                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                resolve({
                    success: true,
                    message: user.role === 'freelancer'
                        ? 'Email verified successfully! Your freelancer account is now pending admin approval.'
                        : 'Email verified successfully! You can now log in to your account.'
                });
            }, 500);
        });
    }

    async resendVerificationEmail(email) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const users = this.getUsersFromStorage();
                const user = users.find(u => u.email === email);

                if (!user) {
                    resolve({
                        success: false,
                        message: 'No account found with this email address.'
                    });
                    return;
                }

                if (user.security && user.security.emailVerified) {
                    resolve({
                        success: false,
                        message: 'Email is already verified.'
                    });
                    return;
                }

                // Generate new verification token
                if (!user.security) user.security = {};
                user.security.emailVerificationToken = this.generateToken();

                const userIndex = users.findIndex(u => u.id === user.id);
                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                this.sendVerificationEmail(user);

                resolve({
                    success: true,
                    message: 'Verification email has been resent. Please check your inbox.'
                });
            }, 500);
        });
    }

    // Utility Methods
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateToken() {
        return Math.random().toString(36).substr(2) + Date.now().toString(36);
    }

    // Password Security
    validatePassword(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        if (password.length < minLength) {
            return {
                isValid: false,
                message: `Password must be at least ${minLength} characters long.`
            };
        }

        if (!hasUpperCase) {
            return {
                isValid: false,
                message: 'Password must contain at least one uppercase letter.'
            };
        }

        if (!hasLowerCase) {
            return {
                isValid: false,
                message: 'Password must contain at least one lowercase letter.'
            };
        }

        if (!hasNumbers) {
            return {
                isValid: false,
                message: 'Password must contain at least one number.'
            };
        }

        if (!hasSpecialChar) {
            return {
                isValid: false,
                message: 'Password must contain at least one special character (!@#$%^&*(),.?":{}|<>).'
            };
        }

        return {
            isValid: true,
            message: 'Password is strong.'
        };
    }

    // Simple password hashing (in production, use bcrypt or similar)
    hashPassword(password) {
        // This is a simple hash for demo purposes
        // In production, use proper hashing like bcrypt
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return 'hash_' + Math.abs(hash).toString(36) + '_' + password.length;
    }

    verifyPassword(password, hashedPassword) {
        // For demo purposes, compare with our simple hash
        return this.hashPassword(password) === hashedPassword;
    }

    // Email Simulation Methods
    sendVerificationEmail(user) {
        const verificationLink = `${window.location.origin}/verify-email.html?token=${user.security.emailVerificationToken}`;

        // Simulate email sending
        console.log(`
=== EMAIL VERIFICATION ===
To: ${user.email}
Subject: Verify Your Portfolio Pro Account

Dear ${user.fullName},

Thank you for registering with Portfolio Pro! Please click the link below to verify your email address:

${verificationLink}

If you didn't create this account, please ignore this email.

Best regards,
Portfolio Pro Team
===========================
        `);

        // Store email in localStorage for demo purposes
        this.storeEmailNotification({
            type: 'verification',
            to: user.email,
            subject: 'Verify Your Portfolio Pro Account',
            link: verificationLink,
            timestamp: new Date().toISOString()
        });
    }

    sendPasswordResetEmail(user, resetToken) {
        const resetLink = `${window.location.origin}/reset-password.html?token=${resetToken}`;

        // Simulate email sending
        console.log(`
=== PASSWORD RESET ===
To: ${user.email}
Subject: Reset Your Portfolio Pro Password

Dear ${user.fullName},

You requested to reset your password for your Portfolio Pro account. Click the link below to reset your password:

${resetLink}

This link will expire in 1 hour. If you didn't request this reset, please ignore this email.

Best regards,
Portfolio Pro Team
===================
        `);

        // Store email in localStorage for demo purposes
        this.storeEmailNotification({
            type: 'password_reset',
            to: user.email,
            subject: 'Reset Your Portfolio Pro Password',
            link: resetLink,
            timestamp: new Date().toISOString()
        });
    }

    storeEmailNotification(emailData) {
        const emails = JSON.parse(localStorage.getItem('portfolioPro_emails') || '[]');
        emails.push(emailData);
        // Keep only last 50 emails
        if (emails.length > 50) {
            emails.splice(0, emails.length - 50);
        }
        localStorage.setItem('portfolioPro_emails', JSON.stringify(emails));
    }

    getEmailNotifications() {
        return JSON.parse(localStorage.getItem('portfolioPro_emails') || '[]');
    }

    // Profile Management
    async updateProfile(profileData) {
        return new Promise((resolve) => {
            setTimeout(() => {
                if (!this.currentUser) {
                    resolve({
                        success: false,
                        message: 'You must be logged in to update your profile.'
                    });
                    return;
                }

                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u => u.id === this.currentUser.id);

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'User not found.'
                    });
                    return;
                }

                // Update profile data
                const user = users[userIndex];
                user.fullName = profileData.fullName || user.fullName;
                user.profileData = { ...user.profileData, ...profileData.profileData };

                // If email is being changed, require verification
                if (profileData.email && profileData.email !== user.email) {
                    user.email = profileData.email;
                    user.security.emailVerified = false;
                    user.security.emailVerificationToken = this.generateToken();
                    this.sendVerificationEmail(user);
                }

                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                // Update current session
                this.currentUser = user;
                this.saveSession(user);

                resolve({
                    success: true,
                    user: user,
                    message: profileData.email && profileData.email !== users[userIndex].email
                        ? 'Profile updated successfully! Please verify your new email address.'
                        : 'Profile updated successfully!'
                });
            }, 500);
        });
    }

    async changePassword(currentPassword, newPassword) {
        return new Promise((resolve) => {
            setTimeout(() => {
                if (!this.currentUser) {
                    resolve({
                        success: false,
                        message: 'You must be logged in to change your password.'
                    });
                    return;
                }

                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u => u.id === this.currentUser.id);

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'User not found.'
                    });
                    return;
                }

                const user = users[userIndex];

                // Verify current password
                if (!this.verifyPassword(currentPassword, user.password)) {
                    resolve({
                        success: false,
                        message: 'Current password is incorrect.'
                    });
                    return;
                }

                // Validate new password
                const passwordValidation = this.validatePassword(newPassword);
                if (!passwordValidation.isValid) {
                    resolve({
                        success: false,
                        message: passwordValidation.message
                    });
                    return;
                }

                // Update password
                user.password = this.hashPassword(newPassword);
                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                resolve({
                    success: true,
                    message: 'Password changed successfully!'
                });
            }, 800);
        });
    }

    // Account Security
    async enableTwoFactor() {
        return new Promise((resolve) => {
            setTimeout(() => {
                if (!this.currentUser) {
                    resolve({
                        success: false,
                        message: 'You must be logged in to enable two-factor authentication.'
                    });
                    return;
                }

                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u => u.id === this.currentUser.id);

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'User not found.'
                    });
                    return;
                }

                const user = users[userIndex];
                if (!user.security) user.security = {};
                user.security.twoFactorEnabled = true;
                user.security.twoFactorSecret = this.generateToken(); // In real app, use proper 2FA secret

                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                // Update current session
                this.currentUser = user;
                this.saveSession(user);

                resolve({
                    success: true,
                    message: 'Two-factor authentication has been enabled for your account.'
                });
            }, 500);
        });
    }

    async disableTwoFactor() {
        return new Promise((resolve) => {
            setTimeout(() => {
                if (!this.currentUser) {
                    resolve({
                        success: false,
                        message: 'You must be logged in to disable two-factor authentication.'
                    });
                    return;
                }

                const users = this.getUsersFromStorage();
                const userIndex = users.findIndex(u => u.id === this.currentUser.id);

                if (userIndex === -1) {
                    resolve({
                        success: false,
                        message: 'User not found.'
                    });
                    return;
                }

                const user = users[userIndex];
                if (!user.security) user.security = {};
                user.security.twoFactorEnabled = false;
                user.security.twoFactorSecret = null;

                users[userIndex] = user;
                localStorage.setItem('portfolioPro_users', JSON.stringify(users));

                // Update current session
                this.currentUser = user;
                this.saveSession(user);

                resolve({
                    success: true,
                    message: 'Two-factor authentication has been disabled for your account.'
                });
            }, 500);
        });
    }

    getAccountSecurityStatus() {
        if (!this.currentUser || !this.currentUser.security) {
            return {
                emailVerified: false,
                twoFactorEnabled: false,
                lastLogin: null,
                accountLocked: false
            };
        }

        const security = this.currentUser.security;
        return {
            emailVerified: security.emailVerified || false,
            twoFactorEnabled: security.twoFactorEnabled || false,
            lastLogin: security.lastLogin,
            accountLocked: security.lockedUntil && new Date() < new Date(security.lockedUntil)
        };
    }

    getDefaultProfileData(role) {
        const baseProfile = {
            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=120&h=120&fit=crop&crop=face',
            location: '',
            bio: '',
            joinDate: new Date().toISOString()
        };

        switch (role) {
            case 'freelancer':
                return {
                    ...baseProfile,
                    skills: [],
                    hourlyRate: 0,
                    portfolio: [],
                    rating: 0,
                    completedProjects: 0
                };
            case 'client':
                return {
                    ...baseProfile,
                    company: '',
                    projectsPosted: 0,
                    totalSpent: 0
                };
            case 'admin':
                return {
                    ...baseProfile,
                    permissions: ['all']
                };
            default:
                return baseProfile;
        }
    }

    saveUserToStorage(userData) {
        const users = this.getUsersFromStorage();
        users.push(userData);
        localStorage.setItem('portfolioPro_users', JSON.stringify(users));
    }

    getUsersFromStorage() {
        const users = localStorage.getItem('portfolioPro_users');
        return users ? JSON.parse(users) : [];
    }

    // Event Listeners
    setupEventListeners() {
        // Login form
        const loginForm = document.querySelector('#loginForm form');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(loginForm);
                const email = formData.get('email');
                const password = formData.get('password');
                
                const loginBtn = loginForm.querySelector('button[type="submit"]');
                const originalText = loginBtn.textContent;
                loginBtn.textContent = 'Signing In...';
                loginBtn.disabled = true;
                
                try {
                    const result = await this.login(email, password);
                    if (result.success) {
                        this.showMessage(result.message, 'success');
                        setTimeout(() => {
                            this.redirectToDashboard();
                        }, 1000);
                    } else {
                        this.showMessage(result.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('Login failed. Please try again.', 'error');
                } finally {
                    loginBtn.textContent = originalText;
                    loginBtn.disabled = false;
                }
            });
        }

        // Register form
        const registerForm = document.querySelector('#registerForm form');
        if (registerForm) {
            registerForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(registerForm);
                
                const registerBtn = registerForm.querySelector('button[type="submit"]');
                const originalText = registerBtn.textContent;
                registerBtn.textContent = 'Creating Account...';
                registerBtn.disabled = true;
                
                try {
                    const result = await this.register(formData);
                    if (result.success) {
                        this.showMessage(result.message, 'success');
                        setTimeout(() => {
                            this.redirectToDashboard();
                        }, 1500);
                    } else {
                        this.showMessage(result.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('Registration failed. Please try again.', 'error');
                } finally {
                    registerBtn.textContent = originalText;
                    registerBtn.disabled = false;
                }
            });
        }

        // Logout buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.logout-btn') || e.target.closest('.logout-btn')) {
                e.preventDefault();
                this.logout();
            }
        });
    }

    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.auth-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'auth-message';
            const authModal = document.querySelector('#authModal .modal-content');
            if (authModal) {
                authModal.insertBefore(messageEl, authModal.firstChild);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `auth-message ${type}`;
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    // Public API
    getCurrentUser() {
        return this.currentUser;
    }

    isLoggedIn() {
        return !!this.currentUser;
    }

    getUserRole() {
        return this.currentUser ? this.currentUser.role : null;
    }

    // Logout function
    logout() {
        this.clearSession();
        window.location.href = 'index.html';
    }
}

// Initialize authentication system
const authSystem = new AuthSystem();

// Export for global use
window.authSystem = authSystem;
