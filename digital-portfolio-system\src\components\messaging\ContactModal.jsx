import { useState } from 'react'
import { 
  X, 
  Send, 
  User, 
  Mail, 
  MessageCircle, 
  Briefcase, 
  DollarSign,
  Calendar,
  Clock,
  Star,
  MapPin
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

const ContactModal = ({ isOpen, onClose, freelancer, onSendMessage }) => {
  const { user, isAuthenticated } = useAuth()
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
    projectType: '',
    budget: '',
    timeline: '',
    attachments: []
  })
  const [sending, setSending] = useState(false)

  const projectTypes = [
    'Web Development',
    'Mobile App Development',
    'UI/UX Design',
    'Graphic Design',
    'Digital Marketing',
    'Content Writing',
    'Data Analysis',
    'Consulting',
    'Other'
  ]

  const budgetRanges = [
    'Under $500',
    '$500 - $1,000',
    '$1,000 - $5,000',
    '$5,000 - $10,000',
    '$10,000 - $25,000',
    '$25,000+',
    'Hourly Rate',
    'To be discussed'
  ]

  const timelineOptions = [
    'ASAP',
    'Within 1 week',
    'Within 2 weeks',
    'Within 1 month',
    '1-3 months',
    '3-6 months',
    '6+ months',
    'Ongoing'
  ]

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!isAuthenticated) {
      alert('Please log in to send messages')
      return
    }

    setSending(true)
    try {
      // In a real app, this would send to your backend API
      const messageData = {
        from: user,
        to: freelancer,
        ...formData,
        timestamp: new Date().toISOString(),
        id: Date.now()
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onSendMessage && onSendMessage(messageData)
      
      // Reset form
      setFormData({
        subject: '',
        message: '',
        projectType: '',
        budget: '',
        timeline: '',
        attachments: []
      })
      
      alert('Message sent successfully!')
      onClose()
    } catch (error) {
      alert('Error sending message. Please try again.')
    } finally {
      setSending(false)
    }
  }

  if (!isOpen || !freelancer) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <img
              src={freelancer.avatar}
              alt={freelancer.name}
              className="w-12 h-12 rounded-full object-cover"
            />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Contact {freelancer.name}
              </h2>
              <p className="text-gray-600">{freelancer.title}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Freelancer Info Summary */}
        <div className="p-6 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <Star className="w-4 h-4 text-yellow-400 mr-1" />
              <span>{freelancer.rating} ({freelancer.reviewCount} reviews)</span>
            </div>
            <div className="flex items-center text-gray-600">
              <DollarSign className="w-4 h-4 mr-1" />
              <span>${freelancer.hourlyRate}/hr</span>
            </div>
            <div className="flex items-center text-gray-600">
              <Clock className="w-4 h-4 mr-1" />
              <span>{freelancer.responseTime} response</span>
            </div>
            <div className="flex items-center text-gray-600">
              <MapPin className="w-4 h-4 mr-1" />
              <span>{freelancer.location}</span>
            </div>
          </div>
          <div className="mt-3">
            <div className="flex flex-wrap gap-1">
              {freelancer.skills?.slice(0, 5).map((skill) => (
                <span
                  key={skill}
                  className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Project Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Briefcase className="w-4 h-4 inline mr-1" />
              Project Type
            </label>
            <select
              value={formData.projectType}
              onChange={(e) => handleInputChange('projectType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            >
              <option value="">Select project type</option>
              {projectTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* Subject */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject *
            </label>
            <input
              type="text"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              placeholder="Brief description of your project"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            />
          </div>

          {/* Budget and Timeline */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="w-4 h-4 inline mr-1" />
                Budget Range
              </label>
              <select
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Select budget range</option>
                {budgetRanges.map((range) => (
                  <option key={range} value={range}>{range}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                Timeline
              </label>
              <select
                value={formData.timeline}
                onChange={(e) => handleInputChange('timeline', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Select timeline</option>
                {timelineOptions.map((option) => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MessageCircle className="w-4 h-4 inline mr-1" />
              Message *
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              placeholder="Describe your project in detail. Include requirements, expectations, and any specific questions you have for this freelancer."
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              {formData.message.length}/1000 characters
            </p>
          </div>

          {/* Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Tips for a great message:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Be specific about your project requirements</li>
              <li>• Mention why you chose this freelancer</li>
              <li>• Include your timeline and budget expectations</li>
              <li>• Ask relevant questions about their experience</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              {!isAuthenticated && (
                <span className="text-red-600">Please log in to send messages</span>
              )}
            </div>
            <div className="flex gap-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={sending || !isAuthenticated}
                className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-4 h-4 mr-2" />
                {sending ? 'Sending...' : 'Send Message'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ContactModal
