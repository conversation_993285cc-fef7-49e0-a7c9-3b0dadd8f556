import { useState } from 'react'
import { Search, Filter, MapPin, DollarSign, Clock } from 'lucide-react'

const SearchFilters = ({ onFilterChange }) => {
  const [filters, setFilters] = useState({
    search: '',
    skills: [],
    experience: '',
    location: '',
    priceRange: '',
    availability: ''
  })

  const skillOptions = [
    'Web Development', 'Mobile Development', 'UI/UX Design', 'Graphic Design',
    'Content Writing', 'Digital Marketing', 'Data Analysis', 'Photography',
    'Video Editing', 'Translation', 'Virtual Assistant', 'Consulting'
  ]

  const experienceOptions = [
    'Entry Level (0-1 years)',
    'Intermediate (2-4 years)',
    'Experienced (5-7 years)',
    'Expert (8+ years)'
  ]

  const priceRanges = [
    '$5-$15/hour',
    '$15-$30/hour',
    '$30-$50/hour',
    '$50-$100/hour',
    '$100+/hour'
  ]

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  const handleSkillToggle = (skill) => {
    const newSkills = filters.skills.includes(skill)
      ? filters.skills.filter(s => s !== skill)
      : [...filters.skills, skill]
    
    handleFilterChange('skills', newSkills)
  }

  const clearFilters = () => {
    const emptyFilters = {
      search: '',
      skills: [],
      experience: '',
      location: '',
      priceRange: '',
      availability: ''
    }
    setFilters(emptyFilters)
    onFilterChange(emptyFilters)
  }

  return (
    <div className="glass-card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2 text-white">
            <Filter className="w-5 h-5" />
            Filters
          </h3>
          <button
            onClick={clearFilters}
            className="text-sm text-accent-400 hover:text-accent-300 transition-colors"
          >
            Clear All
          </button>
        </div>
      </div>
      
      <div className="card-body space-y-6">
        {/* Search */}
        <div>
          <label className="form-label text-gray-300">
            <Search className="w-4 h-4 inline mr-2" />
            Search
          </label>
          <input
            type="text"
            className="form-input bg-gray-800/50 border-gray-600 text-white placeholder-gray-400"
            placeholder="Search freelancers..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
          />
        </div>

        {/* Skills */}
        <div>
          <label className="form-label text-gray-300">Skills</label>
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {skillOptions.map((skill) => (
              <label key={skill} className="flex items-center cursor-pointer group">
                <input
                  type="checkbox"
                  className="mr-3 w-4 h-4 text-accent-600 bg-gray-800 border-gray-600 rounded focus:ring-accent-500"
                  checked={filters.skills.includes(skill)}
                  onChange={() => handleSkillToggle(skill)}
                />
                <span className="text-sm text-gray-300 group-hover:text-white transition-colors">{skill}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Experience Level */}
        <div>
          <label className="form-label text-gray-300">
            <Clock className="w-4 h-4 inline mr-2" />
            Experience Level
          </label>
          <select
            className="form-input bg-gray-800/50 border-gray-600 text-white"
            value={filters.experience}
            onChange={(e) => handleFilterChange('experience', e.target.value)}
          >
            <option value="">Any Experience</option>
            {experienceOptions.map((exp) => (
              <option key={exp} value={exp}>{exp}</option>
            ))}
          </select>
        </div>

        {/* Location */}
        <div>
          <label className="form-label text-gray-300">
            <MapPin className="w-4 h-4 inline mr-2" />
            Location
          </label>
          <input
            type="text"
            className="form-input bg-gray-800/50 border-gray-600 text-white placeholder-gray-400"
            placeholder="City, Country"
            value={filters.location}
            onChange={(e) => handleFilterChange('location', e.target.value)}
          />
        </div>

        {/* Price Range */}
        <div>
          <label className="form-label text-gray-300">
            <DollarSign className="w-4 h-4 inline mr-2" />
            Hourly Rate
          </label>
          <select
            className="form-input bg-gray-800/50 border-gray-600 text-white"
            value={filters.priceRange}
            onChange={(e) => handleFilterChange('priceRange', e.target.value)}
          >
            <option value="">Any Rate</option>
            {priceRanges.map((range) => (
              <option key={range} value={range}>{range}</option>
            ))}
          </select>
        </div>

        {/* Availability */}
        <div>
          <label className="form-label text-gray-300">Availability</label>
          <select
            className="form-input bg-gray-800/50 border-gray-600 text-white"
            value={filters.availability}
            onChange={(e) => handleFilterChange('availability', e.target.value)}
          >
            <option value="">Any Availability</option>
            <option value="available">Available Now</option>
            <option value="busy">Busy</option>
            <option value="part-time">Part-time Only</option>
          </select>
        </div>
      </div>
    </div>
  )
}

export default SearchFilters
