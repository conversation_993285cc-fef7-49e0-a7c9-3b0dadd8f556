@echo off
echo ========================================
echo   DIGITAL PORTFOLIO SYSTEM - QUICK START
echo ========================================
echo.

REM Set Node.js path
set "PATH=C:\Program Files\nodejs;%PATH%"

REM Change to project directory
cd /d "%~dp0"

echo Testing Node.js...
node --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

echo Testing npm...
npm --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: npm not found!
    pause
    exit /b 1
)

echo.
echo ========================================
echo   STARTING DEVELOPMENT SERVER...
echo ========================================
echo.
echo Your Fiverr-style portfolio will open at:
echo http://localhost:5173
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

echo.
echo Server stopped.
pause
