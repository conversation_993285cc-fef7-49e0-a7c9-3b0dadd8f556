import { useState } from 'react'
import { 
  Star, 
  MapPin, 
  Clock, 
  DollarSign, 
  Eye, 
  Heart, 
  MessageCircle,
  ExternalLink,
  Filter,
  Grid,
  List,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

const SearchResults = ({ 
  results = [], 
  loading = false, 
  totalResults = 0, 
  currentPage = 1, 
  totalPages = 1,
  onPageChange,
  onContactUser,
  onSaveProfile,
  onViewProfile 
}) => {
  const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
  const [sortBy, setSortBy] = useState('relevance')

  // Mock data for demonstration
  const mockResults = results.length > 0 ? results : [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Full-Stack Developer',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      location: 'San Francisco, CA',
      hourlyRate: 85,
      rating: 4.9,
      reviewCount: 127,
      skills: ['React', 'Node.js', 'TypeScript', 'AWS'],
      description: 'Experienced full-stack developer with 6+ years building scalable web applications. Specialized in React, Node.js, and cloud architecture.',
      availability: 'Available',
      completedProjects: 89,
      responseTime: '2 hours',
      portfolio: {
        images: [
          'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300',
          'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300'
        ]
      },
      verified: true,
      lastActive: '2 hours ago'
    },
    {
      id: 2,
      name: 'Michael Chen',
      title: 'UI/UX Designer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      location: 'New York, NY',
      hourlyRate: 75,
      rating: 4.8,
      reviewCount: 94,
      skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      description: 'Creative UI/UX designer focused on creating intuitive and beautiful user experiences. 5+ years of experience with startups and enterprises.',
      availability: 'Available',
      completedProjects: 67,
      responseTime: '1 hour',
      portfolio: {
        images: [
          'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=300',
          'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=300'
        ]
      },
      verified: true,
      lastActive: '1 hour ago'
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      title: 'Digital Marketing Specialist',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      location: 'Austin, TX',
      hourlyRate: 60,
      rating: 4.7,
      reviewCount: 156,
      skills: ['SEO', 'Google Ads', 'Content Marketing', 'Analytics'],
      description: 'Results-driven digital marketer with proven track record of increasing online visibility and driving conversions for businesses.',
      availability: 'Busy',
      completedProjects: 134,
      responseTime: '4 hours',
      portfolio: {
        images: [
          'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300'
        ]
      },
      verified: true,
      lastActive: '30 minutes ago'
    }
  ]

  const sortOptions = [
    { value: 'relevance', label: 'Most Relevant' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'recent', label: 'Most Recent' }
  ]

  const ProfileCard = ({ profile, isListView = false }) => (
    <div className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 ${
      isListView ? 'p-6' : 'p-4'
    }`}>
      <div className={`flex ${isListView ? 'gap-6' : 'flex-col'}`}>
        {/* Avatar and Basic Info */}
        <div className={`${isListView ? 'flex-shrink-0' : 'text-center mb-4'}`}>
          <div className="relative">
            <img
              src={profile.avatar}
              alt={profile.name}
              className={`${isListView ? 'w-20 h-20' : 'w-16 h-16 mx-auto'} rounded-full object-cover`}
            />
            {profile.verified && (
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
          {!isListView && (
            <div className="mt-2">
              <h3 className="font-semibold text-gray-900">{profile.name}</h3>
              <p className="text-sm text-gray-600">{profile.title}</p>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {isListView && (
            <div className="mb-3">
              <h3 className="text-lg font-semibold text-gray-900">{profile.name}</h3>
              <p className="text-gray-600">{profile.title}</p>
            </div>
          )}

          {/* Rating and Location */}
          <div className={`flex items-center gap-4 ${isListView ? 'mb-3' : 'mb-2 justify-center text-sm'}`}>
            <div className="flex items-center">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="ml-1 font-medium">{profile.rating}</span>
              <span className="text-gray-500 text-sm ml-1">({profile.reviewCount})</span>
            </div>
            <div className="flex items-center text-gray-600">
              <MapPin className="w-4 h-4" />
              <span className="ml-1 text-sm">{profile.location}</span>
            </div>
          </div>

          {/* Description */}
          {isListView && (
            <p className="text-gray-700 mb-3 line-clamp-2">{profile.description}</p>
          )}

          {/* Skills */}
          <div className={`${isListView ? 'mb-4' : 'mb-3'}`}>
            <div className="flex flex-wrap gap-1">
              {profile.skills.slice(0, isListView ? 6 : 4).map((skill) => (
                <span
                  key={skill}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                >
                  {skill}
                </span>
              ))}
              {profile.skills.length > (isListView ? 6 : 4) && (
                <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">
                  +{profile.skills.length - (isListView ? 6 : 4)}
                </span>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className={`grid grid-cols-2 gap-2 text-sm text-gray-600 ${isListView ? 'mb-4' : 'mb-3'}`}>
            <div className="flex items-center">
              <DollarSign className="w-4 h-4" />
              <span className="ml-1">${profile.hourlyRate}/hr</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4" />
              <span className="ml-1">{profile.responseTime}</span>
            </div>
          </div>

          {/* Portfolio Preview */}
          {isListView && profile.portfolio?.images && (
            <div className="mb-4">
              <div className="flex gap-2">
                {profile.portfolio.images.slice(0, 3).map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`Portfolio ${index + 1}`}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className={`${isListView ? 'flex-shrink-0 flex flex-col gap-2' : 'flex gap-2'}`}>
          <button
            onClick={() => onViewProfile && onViewProfile(profile)}
            className={`${isListView ? 'px-4 py-2' : 'flex-1 px-3 py-2'} bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors`}
          >
            View Profile
          </button>
          <button
            onClick={() => onContactUser && onContactUser(profile)}
            className={`${isListView ? 'px-4 py-2' : 'flex-1 px-3 py-2'} border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors`}
          >
            <MessageCircle className="w-4 h-4 inline mr-1" />
            Contact
          </button>
          {!isListView && (
            <button
              onClick={() => onSaveProfile && onSaveProfile(profile)}
              className="p-2 text-gray-400 hover:text-red-500 transition-colors"
            >
              <Heart className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Availability Status */}
      <div className={`${isListView ? 'mt-4 pt-4 border-t border-gray-100' : 'mt-3 pt-3 border-t border-gray-100'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              profile.availability === 'Available' ? 'bg-green-400' : 'bg-yellow-400'
            }`} />
            <span className="text-sm text-gray-600">{profile.availability}</span>
          </div>
          <span className="text-xs text-gray-500">Active {profile.lastActive}</span>
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
            <div className="flex gap-4">
              <div className="w-16 h-16 bg-gray-200 rounded-full" />
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2" />
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4" />
                <div className="flex gap-2 mb-3">
                  <div className="h-6 bg-gray-200 rounded-full w-16" />
                  <div className="h-6 bg-gray-200 rounded-full w-20" />
                  <div className="h-6 bg-gray-200 rounded-full w-18" />
                </div>
                <div className="h-3 bg-gray-200 rounded w-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {totalResults.toLocaleString()} Portfolio{totalResults !== 1 ? 's' : ''} Found
          </h2>
          <p className="text-gray-600 text-sm mt-1">
            Showing {((currentPage - 1) * 12) + 1}-{Math.min(currentPage * 12, totalResults)} of {totalResults} results
          </p>
        </div>

        <div className="flex items-center gap-4">
          {/* Sort Dropdown */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-lg">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Results Grid/List */}
      <div className={viewMode === 'grid' 
        ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
        : 'space-y-4'
      }>
        {mockResults.map((profile) => (
          <ProfileCard 
            key={profile.id} 
            profile={profile} 
            isListView={viewMode === 'list'}
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 mt-8">
          <button
            onClick={() => onPageChange && onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-2 text-gray-600 hover:text-gray-900 disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          
          {[...Array(Math.min(5, totalPages))].map((_, i) => {
            const pageNum = i + 1
            return (
              <button
                key={pageNum}
                onClick={() => onPageChange && onPageChange(pageNum)}
                className={`px-3 py-2 text-sm font-medium rounded-lg ${
                  currentPage === pageNum
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {pageNum}
              </button>
            )
          })}
          
          <button
            onClick={() => onPageChange && onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="p-2 text-gray-600 hover:text-gray-900 disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>
      )}
    </div>
  )
}

export default SearchResults
