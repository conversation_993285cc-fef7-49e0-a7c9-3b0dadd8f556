import { useState, useRef } from 'react'
import { 
  User, 
  Camera, 
  Upload, 
  X, 
  Plus, 
  Save, 
  Eye, 
  Share2,
  MapPin,
  Phone,
  Mail,
  Globe,
  Briefcase,
  GraduationCap,
  Award,
  Link as LinkIcon,
  FileText,
  Image as ImageIcon,
  Video
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

const EnhancedProfileBuilder = () => {
  const { user, updateUser } = useAuth()
  const [activeTab, setActiveTab] = useState('basic')
  const [saving, setSaving] = useState(false)
  const [profileData, setProfileData] = useState({
    // Basic Information
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    title: user?.title || '',
    bio: user?.bio || '',
    location: user?.location || '',
    phone: user?.phone || '',
    email: user?.email || '',
    website: user?.website || '',
    avatar: user?.avatar || '',
    
    // Professional Information
    hourlyRate: user?.hourlyRate || '',
    availability: user?.availability || 'Available',
    experience: user?.experience || '',
    skills: user?.skills || [],
    languages: user?.languages || [],
    
    // Work Experience
    workExperience: user?.workExperience || [],
    
    // Education
    education: user?.education || [],
    
    // Portfolio/Work Samples
    portfolio: user?.portfolio || [],
    
    // Certifications
    certifications: user?.certifications || [],
    
    // Social Links
    socialLinks: user?.socialLinks || {}
  })

  const fileInputRef = useRef(null)
  const portfolioFileRef = useRef(null)

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: User },
    { id: 'professional', label: 'Professional', icon: Briefcase },
    { id: 'experience', label: 'Experience', icon: GraduationCap },
    { id: 'portfolio', label: 'Portfolio', icon: FileText },
    { id: 'certifications', label: 'Certifications', icon: Award }
  ]

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayAdd = (field, item) => {
    setProfileData(prev => ({
      ...prev,
      [field]: [...prev[field], item]
    }))
  }

  const handleArrayRemove = (field, index) => {
    setProfileData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const handleArrayUpdate = (field, index, updatedItem) => {
    setProfileData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? updatedItem : item)
    }))
  }

  const handleFileUpload = (event, type = 'avatar') => {
    const file = event.target.files[0]
    if (!file) return

    // In a real app, you would upload to a server or cloud storage
    const reader = new FileReader()
    reader.onload = (e) => {
      if (type === 'avatar') {
        handleInputChange('avatar', e.target.result)
      } else if (type === 'portfolio') {
        const portfolioItem = {
          id: Date.now(),
          title: file.name,
          type: file.type.startsWith('image/') ? 'image' : 'document',
          url: e.target.result,
          description: '',
          uploadedAt: new Date().toISOString()
        }
        handleArrayAdd('portfolio', portfolioItem)
      }
    }
    reader.readAsDataURL(file)
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // In a real app, this would save to your backend
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update user context
      updateUser({ ...user, ...profileData })
      
      // Save to localStorage for demo
      localStorage.setItem('userProfile', JSON.stringify(profileData))
      
      alert('Profile saved successfully!')
    } catch (error) {
      alert('Error saving profile. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handlePreview = () => {
    // Open profile preview in new window/modal
    alert('Profile preview would open here')
  }

  const handleShare = () => {
    // Generate shareable link
    const shareUrl = `${window.location.origin}/profile/${user?.id}`
    navigator.clipboard.writeText(shareUrl)
    alert('Profile link copied to clipboard!')
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      {/* Avatar Upload */}
      <div className="flex items-center space-x-6">
        <div className="relative">
          <img
            src={profileData.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'}
            alt="Profile"
            className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            className="absolute bottom-0 right-0 bg-primary-600 text-white p-2 rounded-full hover:bg-primary-700 transition-colors"
          >
            <Camera className="w-4 h-4" />
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => handleFileUpload(e, 'avatar')}
            className="hidden"
          />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Profile Photo</h3>
          <p className="text-sm text-gray-600">Upload a professional photo that represents you</p>
        </div>
      </div>

      {/* Basic Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            First Name *
          </label>
          <input
            type="text"
            value={profileData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            value={profileData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Professional Title *
        </label>
        <input
          type="text"
          value={profileData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          placeholder="e.g., Full-Stack Developer, UI/UX Designer"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Bio/Description *
        </label>
        <textarea
          value={profileData.bio}
          onChange={(e) => handleInputChange('bio', e.target.value)}
          placeholder="Tell potential clients about yourself, your experience, and what makes you unique..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          required
        />
        <p className="text-sm text-gray-500 mt-1">
          {profileData.bio.length}/500 characters
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="w-4 h-4 inline mr-1" />
            Location
          </label>
          <input
            type="text"
            value={profileData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="City, State, Country"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Phone className="w-4 h-4 inline mr-1" />
            Phone Number
          </label>
          <input
            type="tel"
            value={profileData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="+****************"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Mail className="w-4 h-4 inline mr-1" />
            Email Address *
          </label>
          <input
            type="email"
            value={profileData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Globe className="w-4 h-4 inline mr-1" />
            Website/Portfolio URL
          </label>
          <input
            type="url"
            value={profileData.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            placeholder="https://yourwebsite.com"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>
  )

  const renderProfessionalInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Hourly Rate (USD)
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
            <input
              type="number"
              value={profileData.hourlyRate}
              onChange={(e) => handleInputChange('hourlyRate', e.target.value)}
              placeholder="50"
              className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Availability Status
          </label>
          <select
            value={profileData.availability}
            onChange={(e) => handleInputChange('availability', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="Available">Available</option>
            <option value="Busy">Busy</option>
            <option value="Unavailable">Unavailable</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Experience Level
        </label>
        <select
          value={profileData.experience}
          onChange={(e) => handleInputChange('experience', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        >
          <option value="">Select Experience Level</option>
          <option value="entry">Entry Level (0-2 years)</option>
          <option value="mid">Mid Level (2-5 years)</option>
          <option value="senior">Senior Level (5-10 years)</option>
          <option value="expert">Expert Level (10+ years)</option>
        </select>
      </div>

      {/* Skills Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Skills & Expertise
        </label>
        <div className="space-y-3">
          <div className="flex flex-wrap gap-2">
            {profileData.skills.map((skill, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
              >
                {skill}
                <button
                  onClick={() => handleArrayRemove('skills', index)}
                  className="ml-2 text-primary-600 hover:text-primary-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Add a skill (e.g., React, Python, UI Design)"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && e.target.value.trim()) {
                  handleArrayAdd('skills', e.target.value.trim())
                  e.target.value = ''
                }
              }}
            />
            <button
              onClick={(e) => {
                const input = e.target.previousElementSibling
                if (input.value.trim()) {
                  handleArrayAdd('skills', input.value.trim())
                  input.value = ''
                }
              }}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Languages Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Languages
        </label>
        <div className="space-y-3">
          <div className="flex flex-wrap gap-2">
            {profileData.languages.map((language, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
              >
                {language}
                <button
                  onClick={() => handleArrayRemove('languages', index)}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Add a language (e.g., English - Native, Spanish - Fluent)"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && e.target.value.trim()) {
                  handleArrayAdd('languages', e.target.value.trim())
                  e.target.value = ''
                }
              }}
            />
            <button
              onClick={(e) => {
                const input = e.target.previousElementSibling
                if (input.value.trim()) {
                  handleArrayAdd('languages', input.value.trim())
                  input.value = ''
                }
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  const renderPortfolio = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Work Samples & Portfolio</h3>
          <p className="text-sm text-gray-600 mt-1">Upload your best work to showcase your skills</p>
        </div>
        <button
          onClick={() => portfolioFileRef.current?.click()}
          className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Upload className="w-4 h-4 mr-2" />
          Upload Work Sample
        </button>
        <input
          ref={portfolioFileRef}
          type="file"
          accept="image/*,.pdf,.doc,.docx"
          onChange={(e) => handleFileUpload(e, 'portfolio')}
          className="hidden"
        />
      </div>

      {/* Portfolio Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {profileData.portfolio.map((item, index) => (
          <div key={item.id} className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
            {item.type === 'image' ? (
              <img
                src={item.url}
                alt={item.title}
                className="w-full h-48 object-cover"
              />
            ) : (
              <div className="w-full h-48 flex items-center justify-center bg-gray-100">
                <FileText className="w-12 h-12 text-gray-400" />
              </div>
            )}
            <div className="p-4">
              <input
                type="text"
                value={item.title}
                onChange={(e) => handleArrayUpdate('portfolio', index, { ...item, title: e.target.value })}
                className="w-full font-medium text-gray-900 bg-transparent border-none p-0 focus:ring-0"
                placeholder="Project title"
              />
              <textarea
                value={item.description}
                onChange={(e) => handleArrayUpdate('portfolio', index, { ...item, description: e.target.value })}
                placeholder="Describe this work sample..."
                rows={2}
                className="w-full mt-2 text-sm text-gray-600 bg-transparent border-none p-0 resize-none focus:ring-0"
              />
              <div className="flex items-center justify-between mt-3">
                <span className="text-xs text-gray-500">
                  {new Date(item.uploadedAt).toLocaleDateString()}
                </span>
                <button
                  onClick={() => handleArrayRemove('portfolio', index)}
                  className="text-red-500 hover:text-red-700 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}

        {/* Upload Placeholder */}
        <button
          onClick={() => portfolioFileRef.current?.click()}
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
        >
          <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Click to upload work sample</p>
          <p className="text-xs text-gray-500 mt-1">Images, PDFs, or documents</p>
        </button>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Profile Builder</h1>
                <p className="text-gray-600 mt-1">Create and manage your professional profile</p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handlePreview}
                  className="flex items-center px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </button>
                <button
                  onClick={handleShare}
                  className="flex items-center px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {saving ? 'Saving...' : 'Save Profile'}
                </button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="px-6">
            <nav className="flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            {activeTab === 'basic' && renderBasicInfo()}
            {activeTab === 'professional' && renderProfessionalInfo()}
            {activeTab === 'portfolio' && renderPortfolio()}
            {/* Other tabs would be implemented similarly */}
          </div>
        </div>
      </div>
    </div>
  )
}

export default EnhancedProfileBuilder
