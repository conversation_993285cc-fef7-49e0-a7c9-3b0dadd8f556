import { useAuth } from '../contexts/AuthContext'
import AdminDashboard from './dashboards/AdminDashboard'
import FreelancerDashboard from './dashboards/FreelancerDashboard'
import ClientDashboard from './dashboards/ClientDashboard'
import { AlertCircle } from 'lucide-react'

const DashboardRouter = () => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You need to be logged in to access the dashboard.
          </p>
          <button 
            onClick={() => window.location.href = '/'}
            className="btn btn-primary"
          >
            Go to Home
          </button>
        </div>
      </div>
    )
  }

  // Route to appropriate dashboard based on user role
  switch (user.role) {
    case 'admin':
      return <AdminDashboard />
    case 'freelancer':
      return <FreelancerDashboard />
    case 'client':
      return <ClientDashboard />
    default:
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center max-w-md mx-auto p-6">
            <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Unknown User Role</h2>
            <p className="text-gray-600 mb-4">
              Your account role is not recognized. Please contact support.
            </p>
            <div className="space-x-3">
              <button 
                onClick={() => window.location.href = '/'}
                className="btn btn-outline"
              >
                Go to Home
              </button>
              <button className="btn btn-primary">
                Contact Support
              </button>
            </div>
          </div>
        </div>
      )
  }
}

export default DashboardRouter
